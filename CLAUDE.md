# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

DataHero4 is a monorepo-based conversational AI data analysis system that translates natural language questions into SQL queries, providing insights, visualizations, and follow-up suggestions. Built with FastAPI/LangGraph (backend) and React/TypeScript (frontend).

**Latest Update:** Complete dashboard system with dynamic KPI management - users can add/remove KPIs via visual selection modal with 34 available metrics, real-time calculation, and natural language editing capabilities.

## Essential Commands

### Development
```bash
# Start both backend and frontend
npm run dev

# Backend only (port 8000)
npm run dev:backend

# Frontend only (port 3000)
npm run dev:frontend

# Backend CLI execution (from apps/backend/)
poetry run python src/main.py "question" --client-id L2M --sector cambio
```

### Testing
```bash
# Run all tests via Turbo
npm run test

# Backend tests only
npm run test:backend

# Frontend tests only  
npm run test:frontend

# Backend tests (from apps/backend/)
poetry run pytest
poetry run pytest -v  # verbose
poetry run pytest tests/unit/  # specific directory
poetry run pytest -k "test_name"  # run specific test

# Frontend tests (from apps/frontend/)
npm run test  # runs vitest
```

### Build & Code Quality
```bash
# Build all projects
npm run build

# TypeScript validation
npm run typecheck

# Lint all code
npm run lint

# Format code
npm run format
```

### CLI Tools (from apps/backend/)
```bash
poetry run datahero4          # Enhanced chat CLI
poetry run datahero4-simple   # Simple chat CLI  
poetry run datahero4-feedback # Feedback CLI
```

## Dependency Management

- **Always use Poetry for Python dependencies** in the backend (apps/backend/)
- Use npm/yarn for frontend dependencies (apps/frontend/)
- The monorepo uses Turbo for orchestrating builds/tests
- **ALWAYS use poetry**

## High-Level Architecture

### Backend Pipeline (LangGraph State Machine)
The system uses an optimized workflow with these key components:

1. **Multi-Agent System**:
   - Enhanced Coordinator Agent: Orchestrates the entire workflow with conversational context
   - Business Analyst Agent: Replaces separate insight/question generators for holistic analysis
   - Query Validator Agent: SQL validation with ~20% auto-correction rate
   - SQL Executor Agent: Handles query execution with connection pooling

2. **Performance Optimizations**:
   - Ultra-fast caching: 1.5s response time (85% improvement)
   - Parallel execution nodes for independent operations
   - Confidence-based routing to skip unnecessary validation
   - Circuit breakers to prevent infinite loops
   - 60-70% rate limit reduction through smart caching

3. **Workflow Stages**:
   - Conversational Correction → Check for refinements
   - Parallel Context Prep → Extract entities + check cache simultaneously
   - Feedback Analysis → Process user corrections
   - Context Enhancement → Add schema, KPIs, patterns
   - Query Generation → NL to SQL with confidence scoring
   - Validation → Auto-correction when needed
   - Execution → Run with timeout and error handling
   - Business Analysis → Cached/quick/full modes
   - Learning Update → Update patterns and cache

4. **Dashboard System**:
   - **Dynamic KPI Management**: Visual modal for adding/removing KPIs with search and filtering
   - **34 Available KPIs**: Complete implementation across 7 categories (Volume, Performance, Risk, etc.)
   - **Real-time Calculation**: Live SQL execution with corrected database queries
   - **Visual Selection Interface**: Mini-cards with category badges, icons, and instant preview
   - **Natural Language Editing**: BusinessAnalyst-powered dashboard modifications
   - **Intelligent Alerting**: Context-aware alerts with severity-based prioritization  
   - **Performance Optimized**: Lazy loading, caching, and parallel KPI computation

### Frontend Architecture
- React 18 with TypeScript
- State management: Zustand store for chat + TanStack Query for server state
- UI components: shadcn/ui (Radix UI primitives) with Tailwind CSS
- Real-time: Server-Sent Events (SSE) for streaming responses
- Animations: Motion.dev with pre-defined variants
- Build tool: Vite with SWC for fast development
- **Dashboard Integration**: Real-time KPI cards with interactive charts, dynamic add/remove functionality, and natural language editing
- **KPI Management**: Visual selection modal with 34 available metrics, search/filter capabilities, and instant preview

### Key Directories
```
apps/backend/
├── src/
│   ├── agents/         # LLM agents (coordinator, analyst, validator)
│   ├── graphs/         # LangGraph workflows and state management
│   ├── nodes/          # Pipeline nodes (cache, validation, execution)
│   ├── interfaces/     # FastAPI endpoints, WebSocket handlers, and Dashboard API
│   ├── caching/        # Multi-layer cache (PostgreSQL, Redis, in-memory)
│   ├── config/         # Client/sector configurations
│   ├── services/       # Business logic services (dashboard, KPI calculation)
│   ├── core/           # Core utilities and base classes
│   └── utils/          # Helper functions
└── tests/              # Unit, integration, and performance tests

apps/frontend/
├── src/
│   ├── components/     # UI components organized by feature
│   │   ├── chat/      # Chat interface components
│   │   ├── dashboard/ # Dashboard, KPI cards, and dynamic management components
│   │   │   ├── AddKpiModal.tsx      # Modal for selecting KPIs to add
│   │   │   ├── KpiMiniCard.tsx      # Mini-cards for KPI selection
│   │   │   ├── KpiSearchInput.tsx   # Search component with filtering
│   │   │   ├── KpiCard.tsx          # Main KPI display cards
│   │   │   ├── KpiGrid.tsx          # Grid layout for KPI cards
│   │   │   └── DashboardControls.tsx # Controls including Add KPI button
│   │   └── ui/        # shadcn/ui base components
│   ├── pages/         # Route page components
│   ├── hooks/         # Custom React hooks
│   │   ├── useKpiData.ts        # Main KPI state management with add/remove
│   │   ├── useKpiSelection.ts   # Selection state for KPI modal
│   │   └── useAvailableKpis.ts  # Fetch available KPIs from API
│   └── lib/           # Stores and utilities
└── tests/             # Component and integration tests
```

## Configuration

### Environment Variables
Backend requires:
- `DATABASE_URL`: PostgreSQL connection string
- `TOGETHER_API_KEY`: Primary LLM provider
- `ANTHROPIC_API_KEY`: Optional LLM provider
- `GOOGLE_API_KEY`: Optional LLM provider
- `REDIS_URL`: Redis cache connection (optional)
- `OPENAI_API_KEY`: For embeddings (optional)

### Client/Sector Configuration
Each client has specific configurations in:
```
apps/backend/src/config/setores/{sector}/{client}/
├── L2M_schema.json           # Database schema definition
├── L2M_schema_relevance.json # Table relevance scores
├── llm.yaml                  # LLM provider configuration
├── nivel2/                   # Advanced configurations
│   ├── patterns.json        # Common query patterns
│   ├── semantic_config.json # Semantic mappings
│   └── domain_config.json   # Domain-specific rules
├── prompts/                 # Agent-specific prompts
└── kpis-{sector}.json       # KPI definitions
```

## Testing Strategy

- **Unit tests**: Individual agents, utilities, and components
- **Integration tests**: Full pipeline execution, API endpoints, and dashboard functionality
- **Performance tests**: Latency benchmarks and load testing
- **Regression tests**: Seven Golden Questions validation
- **Dashboard tests**: KPI calculation, real-time updates, and natural language editing
- **E2E tests**: Playwright for frontend flows
- Minimum coverage: 80%

## Development URLs

- **Backend API**: http://localhost:8000
- **Frontend**: http://localhost:3000
- **API Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

### Debug Endpoints
- **Context Metrics**: GET http://localhost:8000/debug/context/metrics
- **Thread Analysis**: GET http://localhost:8000/debug/context/thread/{id}
- **Context Issues**: GET http://localhost:8000/debug/context/issues
- **Context Cleanup**: POST http://localhost:8000/debug/context/cleanup

### Dashboard API Endpoints
- **Dashboard Data**: GET http://localhost:8000/api/dashboard/dashboard
- **All KPIs**: GET http://localhost:8000/api/dashboard/kpis
- **Single KPI**: GET http://localhost:8000/api/dashboard/kpi/{kpi_id}
- **Dashboard Alerts**: GET http://localhost:8000/api/dashboard/alerts
- **Natural Language Edit**: POST http://localhost:8000/api/dashboard/edit
- **Health Check**: GET http://localhost:8000/api/dashboard/health

## Dashboard KPI Management System

### Dynamic KPI Features
- **Visual Selection Modal**: 34 available KPIs displayed as interactive mini-cards
- **Smart Search & Filter**: Real-time filtering by name, description, or category
- **Drag & Drop Support**: Reorder KPIs in the dashboard grid
- **Category Organization**: 7 categories (Volume, Performance, Risk, Operational, etc.)
- **Visual States**: Cards show selected, existing, and available states

### User Workflow
1. **Adding KPIs**: Click "Adicionar KPI" button → Search/browse → Select multiple → Add to dashboard
2. **Removing KPIs**: Hover over KPI card → Click trash icon → Confirm removal
3. **Managing Priority**: Star icon to mark KPIs as priority (larger display)
4. **Real-time Updates**: All data fetched live from corrected database queries

### Technical Implementation
- **Frontend Components**:
  - `AddKpiModal.tsx`: Main selection interface
  - `KpiMiniCard.tsx`: Individual selectable cards
  - `KpiSearchInput.tsx`: Search with debouncing
  - `KpiCard.tsx`: Main dashboard cards with remove option
  
- **State Management**:
  - `useKpiData.ts`: Main hook with add/remove functions
  - `useKpiSelection.ts`: Modal selection state
  - `useAvailableKpis.ts`: Fetch available KPIs from API

- **API Integration**:
  - `getAvailableKpis()`: List all 34 KPIs with metadata
  - `getKPI(id)`: Fetch specific KPI data for new additions
  - All data sourced from corrected database schema (boleta table)

### KPI Categories Available
- **Volume Metrics** (4): Total Volume, Volume by Currency, Average Ticket, Growth %
- **Performance Metrics** (6): Spread, Margins, ROI, Cost ratios
- **Operational Metrics** (8): Transaction counts, processing times, approval rates
- **Risk Metrics** (5): VaR, exposure concentration, compliance scores
- **Quality Metrics** (4): Approval/rejection rates, efficiency metrics
- **Efficiency Metrics** (4): Cost per operation, capacity utilization
- **Growth Metrics** (3): Period comparisons, trend analysis

## Important Notes

1. The system exclusively uses the `optimized_workflow` pipeline
2. Cache invalidation happens automatically on user feedback
3. Parallel nodes execute simultaneously for performance
4. Context preservation maintains temporal and business entities across conversations
5. The SQL validator accepts IN clauses for temporal comparisons
6. Database state is synchronized between PostgreSQL (primary) and SQLite (cache)
7. The system implements segment-level context management (based on SeCom paper)
8. **Dashboard System**: Real-time KPI calculation with 34 metrics across 7 categories
9. **Dynamic KPI Management**: Users can add/remove KPIs via visual selection modal with search
10. **Natural Language Dashboard Editing**: Powered by existing BusinessAnalyst agent

## Performance Features

- **Embedding Model Singleton**: Eliminates 3.2s cold start
- **Schema Compaction Cache**: Reduces 59→8 tables for relevance
- **Context Token Reduction**: 4000→2000 tokens (-50%)
- **Ultra-fast path**: Serves cached queries in 1.5s
- **Smart routing**: Skips validation for high-confidence queries (>0.85)
- **Dashboard Optimization**: Lazy loading, parallel KPI computation, and intelligent caching
- **KPI Selection Performance**: Debounced search, cached available KPIs, and optimized re-renders

## Best Practices

- Não crie arquivos no root - mantenha apenas os necessários, deixando os demais em suas respectivas pastas
- Always check existing code patterns before implementing new features
- Use Poetry for all Python dependency management in backend
- Follow the established multi-agent architecture patterns
- Maintain the LangGraph state machine approach for workflow orchestration

## Project Requirements

É inegociável manter nesse projeto:
- Framework de agentes LangGraph
- LLM calls para generation + analysis
- Validation robusta com auto-correção
- Entity extraction inteligente
- Multi-layer caching strategy
- Conversational context preservation
- **Dashboard integration com KPIs em tempo real**
- **Dynamic KPI management com interface visual**
- **Natural language editing para visualizações**

## Troubleshooting Guidelines

- Quando não conseguir fazer algo relacionado a alguma dependência ou serviço externo, consulte a documentação atual com context7 para saber como fazer
- For LLM provider issues, check the llm.yaml configuration for the specific client/sector
- For cache issues, verify Redis connection and PostgreSQL cache tables
- For frontend streaming issues, ensure SSE is properly configured in the API proxy
- **For dashboard issues**: Check KPI configuration in kpis-exchange-json.json and dashboard API endpoints
- **For KPI calculation errors**: Verify SQL execution nodes and database connectivity
- **For KPI selection modal issues**: Check useAvailableKpis hook and getAvailableKpis API function
- **For add/remove KPI functionality**: Verify useKpiData.addKpis() and removeKpi() functions

## Deployment Notes

- **IMPORTANT**: 
  - lembre-se para nunca usar async calls, porque é incompatível com o railway

## Best Practices

- **Never hardcode** sensitive information, configurations, or environment-specific values