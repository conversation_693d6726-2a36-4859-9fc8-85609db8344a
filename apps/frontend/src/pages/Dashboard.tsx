import React, { useState } from 'react';
import { MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

const Dashboard = () => {
  console.log('Dashboard component rendering - START');

  const navigate = useNavigate();

  console.log('Dashboard component rendering - MIDDLE');

  // Teste simples - retornar apenas um div básico
  return (
    <div style={{ padding: '20px', backgroundColor: 'white', minHeight: '100vh' }}>
      <h1>Dashboard Funcionando!</h1>
      <p>Se você está vendo isso, o componente Dashboard está carregando corretamente.</p>
      <button onClick={() => navigate('/')}>Voltar</button>
    </div>
  );

  /*
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      // O refresh será gerenciado pelo KpiGrid
      window.location.reload();
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleExport = () => {
    // Implementar lógica de exportação
    console.log('Exportando dados...');
  };

  const handleAddKpi = () => {
    setShowAddKpiModal(true);
  };

  const handleKpisSelected = async (kpiIds: string[]) => {
    try {
      // Por enquanto, apenas fechar o modal
      console.log(`✅ ${kpiIds.length} KPIs selecionados:`, kpiIds);
      setShowAddKpiModal(false);
    } catch (error) {
      console.error('❌ Erro ao adicionar KPIs:', error);
    }
  };

  const handleRemoveKpi = (kpiId: string) => {
    console.log(`🗑️ KPI ${kpiId} será removido`);
  };

  // Lista dos IDs dos KPIs existentes para o modal
  const existingKpiIds = kpis.map(kpi => kpi.id);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header simples sem linha separadora */}
      <div className="bg-gray-50 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-6">
            {/* Ícone do DataHero */}
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">AI</span>
            </div>
            
            {/* Indicadores de status */}
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span>Sistema Online</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                <span>Atualizando</span>
              </div>
            </div>
          </div>
          
          {/* Botão para ir às perguntas */}
          <Button 
            variant="outline" 
            onClick={() => navigate('/')}
            className="flex items-center gap-2 hover:bg-gray-50 transition-colors"
          >
            <MessageSquare className="w-4 h-4" />
            Fazer Perguntas
          </Button>
        </div>
      </div>

      {/* Conteúdo principal com melhor espaçamento */}
      <div className="pb-8">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          {/* Teste simples */}
          <div className="bg-white p-4 rounded-lg shadow mb-4">
            <h2 className="text-xl font-bold mb-2">Dashboard Test</h2>
            <p>Se você está vendo isso, o componente Dashboard está funcionando!</p>
          </div>

          {/* Controles do Dashboard */}
          <DashboardControls
            onRefresh={handleRefresh}
            onExport={handleExport}
            onAddKpi={handleAddKpi}
            isRefreshing={isRefreshing}
          />

          {/* Grid de KPIs */}
          <KpiGrid onRemoveKpi={handleRemoveKpi} />
        </div>
      </div>

      // Modal para adicionar KPIs
      // <AddKpiModal
      //   isOpen={showAddKpiModal}
      //   onClose={() => setShowAddKpiModal(false)}
      //   onKpisSelected={handleKpisSelected}
      //   existingKpiIds={[]}
      // />
    // </div>
  // );
  */
};

export default Dashboard;
