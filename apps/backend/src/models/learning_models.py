"""
Learning Models for PostgreSQL - DataHero4
==================================================

SQLAlchemy models for the active learning system using Railway PostgreSQL.
Clean, optimized models for production use.

Active Tables:
- feedback_corrections: User feedback and corrections
- query_cache: Cached queries with embeddings and business analysis
- query_history: Query audit trail and history
"""

from datetime import datetime
from typing import Dict, List, Any
import uuid

from sqlalchemy import (
    Column, String, Text, Float, Boolean, DateTime,
    JSON, LargeBinary, ForeignKey, Index, Integer
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

# Base class for all models
Base = declarative_base()


class KpiDefinition(Base):
    """
    Model for KPI definitions and their generated SQL queries.
    Stores KPI metadata and dynamically generated queries in PostgreSQL.
    """
    __tablename__ = 'kpi_definitions'
    
    # Primary key
    id = Column(String(100), primary_key=True, comment="KPI identifier (e.g., 'total_volume')")
    
    # KPI metadata
    name = Column(String(200), nullable=False, comment="KPI display name")
    description = Column(Text, comment="KPI description")
    category = Column(String(50), comment="KPI category (operational, financial, etc)")
    unit = Column(String(50), comment="Unit of measurement")
    format_type = Column(String(20), comment="Display format (currency, percentage, number)")
    
    # Generated SQL query
    sql_query = Column(Text, comment="Dynamically generated SQL query")
    query_version = Column(Integer, default=1, comment="Query version number")
    query_confidence = Column(Float, comment="Confidence score of generated query")
    
    # Generation metadata
    generated_at = Column(DateTime(timezone=True), comment="When query was generated")
    generated_by = Column(String(50), comment="Generation method (pipeline, manual, etc)")
    last_modified = Column(DateTime(timezone=True), comment="Last modification time")
    last_modified_by = Column(String(100), comment="Who/what modified last")
    
    # Validation and performance
    validation_passed = Column(Boolean, default=True, comment="Query validation status")
    avg_execution_time = Column(Float, comment="Average execution time in seconds")
    last_execution = Column(DateTime(timezone=True), comment="Last time query was executed")
    
    # Additional metadata
    is_active = Column(Boolean, default=True, comment="Whether KPI is active")
    is_priority = Column(Boolean, default=False, comment="Priority KPI flag")
    client_id = Column(String(50), comment="Client identifier if KPI is client-specific")
    sector = Column(String(50), default='cambio', comment="Business sector")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_kpi_sector_client', 'sector', 'client_id'),
        Index('idx_kpi_category', 'category'),
        Index('idx_kpi_active', 'is_active'),
    )


class KpiQueryHistory(Base):
    """
    Model for tracking KPI query modifications and history.
    Maintains version history for audit and rollback capabilities.
    """
    __tablename__ = 'kpi_query_history'
    
    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Foreign key to KPI
    kpi_id = Column(String(100), ForeignKey('kpi_definitions.id'), nullable=False)
    
    # Query version info
    version = Column(Integer, nullable=False, comment="Version number")
    sql_query = Column(Text, nullable=False, comment="SQL query for this version")
    
    # Change metadata
    change_type = Column(String(50), comment="Type of change (generated, manual_edit, nl_edit)")
    change_description = Column(Text, comment="Description of what changed")
    changed_by = Column(String(100), comment="Who/what made the change")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    
    # Relationship
    kpi = relationship("KpiDefinition", backref="query_history")
    
    # Index for performance
    __table_args__ = (
        Index('idx_kpi_history_version', 'kpi_id', 'version'),
    )


class FeedbackCorrection(Base):
    """
    Model for user feedback and corrections.
    Migrated from feedback_store.db - feedback_corrections table.
    """
    __tablename__ = 'feedback_corrections'
    
    # Primary key
    id = Column(String(100), primary_key=True, default=lambda: f"fb_{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8]}")
    
    # Core feedback content
    question = Column(Text, nullable=False, comment="Original user question")
    original_query = Column(Text, nullable=False, comment="Original SQL query generated")
    corrected_query = Column(Text, nullable=False, comment="User-corrected SQL query")
    explanation = Column(Text, comment="User explanation of the correction")

    # Feedback metadata
    feedback_type = Column(String(20), nullable=False, comment="Type of feedback (positive/negative)")
    category = Column(String(50), comment="Feedback category")
    user_comment = Column(Text, comment="User comment/feedback")

    # Client context
    client_id = Column(String(50), default='L2M', comment="Client identifier")
    sector = Column(String(50), default='cambio', comment="Business sector")
    user_id = Column(String(50), comment="User identifier")
    
    # JSON fields for complex data
    patterns_extracted = Column(JSON, comment="Extracted patterns as JSON array")
    meta_data = Column(JSON, comment="Additional metadata as JSON object")
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=func.now(), comment="When feedback was created")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="Last update timestamp")
    
    # Usage statistics
    applied_count = Column(Integer, default=0, comment="Number of times this correction was applied")
    success_rate = Column(Float, default=0.0, comment="Success rate of this correction")
    
    # Cache linking for feedback-aware caching
    query_cache_id = Column(String(100), ForeignKey('query_cache.id'), nullable=True, comment="Link to related cached query")
    
    # Relationships
    query_cache = relationship("QueryCache", back_populates="feedback_corrections")
    
    # Indexes
    __table_args__ = (
        Index('idx_feedback_question', 'question'),
        Index('idx_feedback_created_at', 'created_at'),
        Index('idx_feedback_applied_count', 'applied_count'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'question': self.question,
            'original_query': self.original_query,
            'corrected_query': self.corrected_query,
            'explanation': self.explanation,
            'patterns_extracted': self.patterns_extracted,
            'meta_data': self.meta_data,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'applied_count': self.applied_count,
            'success_rate': self.success_rate
        }





class QueryCache(Base):
    """
    Model for cached queries with embeddings.
    Migrated from query_cache.db - query_cache table.
    """
    __tablename__ = 'query_cache'
    
    # Primary key
    id = Column(String(100), primary_key=True, default=lambda: f"qry_{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8]}")
    
    # Query content
    question = Column(Text, nullable=False, comment="Original user question")
    sql_query = Column(Text, nullable=False, comment="Generated SQL query")
    
    # Embeddings and entities
    embedding = Column(LargeBinary, comment="Pickled embedding vector")
    entities = Column(JSON, nullable=False, comment="Extracted entities as JSON")
    
    # Query metadata
    confidence = Column(Float, nullable=False, default=0.9, comment="Confidence score")
    execution_time = Column(Float, comment="Query execution time in seconds")
    result_count = Column(Integer, comment="Number of results returned")
    source = Column(String(50), nullable=False, default='llm', comment="Query source (llm, user_correction, pattern)")
    meta_data = Column(JSON, comment="Additional metadata")
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=func.now())
    last_used = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Usage statistics
    use_count = Column(Integer, default=1, comment="Number of times query was used")
    
    # Feedback-aware caching columns
    feedback_score = Column(Float, default=None, comment="Calculated feedback score (-1.0 to 1.0)")
    positive_feedback_count = Column(Integer, default=0, comment="Number of positive feedback received")
    negative_feedback_count = Column(Integer, default=0, comment="Number of negative feedback received") 
    last_feedback_date = Column(DateTime, default=None, comment="Date of last feedback received")
    
    # Business analysis cache columns
    business_analysis = Column(JSON, default=None, comment="Cached business analysis from Business Analyst")
    visualization_data = Column(JSON, default=None, comment="Cached visualization data for frontend")
    
    # Relationships
    feedback_corrections = relationship("FeedbackCorrection", back_populates="query_cache")
    
    # Indexes
    __table_args__ = (
        Index('idx_cache_question', 'question'),
        Index('idx_cache_confidence', 'confidence'),
        Index('idx_cache_created_at', 'created_at'),
        Index('idx_cache_last_used', 'last_used'),
        Index('idx_cache_source', 'source'),
        Index('idx_cache_feedback_score', 'feedback_score'),
        Index('idx_cache_feedback_counts', 'positive_feedback_count', 'negative_feedback_count'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'question': self.question,
            'sql_query': self.sql_query,
            'entities': self.entities,
            'confidence': self.confidence,
            'execution_time': self.execution_time,
            'result_count': self.result_count,
            'source': self.source,
            'meta_data': self.meta_data,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_used': self.last_used.isoformat() if self.last_used else None,
            'use_count': self.use_count
        }











# Utility functions for model operations
def create_all_tables(engine):
    """Create all learning tables in PostgreSQL."""
    Base.metadata.create_all(engine)


def drop_all_tables(engine):
    """Drop all learning tables (use with caution!)."""
    Base.metadata.drop_all(engine)


class QueryHistory(Base):
    """
    Model for query history tracking.
    Stores all queries for audit and analysis purposes.
    """
    __tablename__ = 'query_history'

    # Primary key
    id = Column(String(100), primary_key=True)

    # Query content
    question = Column(Text, nullable=False, comment="User question")
    sql_query = Column(Text, nullable=False, comment="Generated SQL query")

    # Client context
    client_id = Column(String(50), comment="Client identifier")
    sector = Column(String(50), default='cambio', comment="Business sector")
    channel = Column(String(50), default='api', comment="Channel (api/cli/whatsapp)")

    # Execution metrics
    execution_time = Column(Float, default=0.0, comment="Query execution time in seconds")
    result_count = Column(Integer, default=0, comment="Number of results returned")
    success = Column(Boolean, default=True, comment="Whether query executed successfully")
    error_message = Column(Text, comment="Error message if query failed")

    # Feedback tracking (legacy fields)
    has_feedback = Column(Boolean, comment="Whether feedback was provided")
    feedback_type = Column(String(20), comment="Type of feedback")
    feedback_explanation = Column(Text, comment="Feedback explanation")
    corrected_query = Column(Text, comment="Corrected SQL query")

    # User context
    user_id = Column(String(50), comment="User identifier")
    session_id = Column(String(100), comment="Session identifier")

    # Timestamps
    timestamp = Column(DateTime, nullable=False, default=datetime.now, comment="Query timestamp")

    # Additional metadata
    meta_data = Column(JSON, comment="Additional metadata as JSON")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'question': self.question,
            'sql_query': self.sql_query,
            'has_feedback': self.has_feedback,
            'feedback_type': self.feedback_type,
            'feedback_explanation': self.feedback_explanation,
            'corrected_query': self.corrected_query,
            'client_id': self.client_id,
            'user_id': self.user_id,
            'session_id': self.session_id,
            'meta_data': self.meta_data
        }


def get_table_names() -> List[str]:
    """Get list of active table names."""
    return [
        'feedback_corrections',
        'query_cache',
        'query_history',
        'kpi_definitions',
        'kpi_query_history'
    ]


def validate_models():
    """Validate active model definitions."""
    errors = []

    # Check that all active models have required fields
    required_models = [
        FeedbackCorrection,
        QueryCache,
        QueryHistory,
        KpiDefinition
    ]

    for model in required_models:
        if not hasattr(model, '__tablename__'):
            errors.append(f"Model {model.__name__} missing __tablename__")

        if not hasattr(model, 'id'):
            errors.append(f"Model {model.__name__} missing id field")

    return errors