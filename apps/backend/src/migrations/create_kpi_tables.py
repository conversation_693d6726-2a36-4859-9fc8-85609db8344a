#!/usr/bin/env python3
"""
Create KPI tables in Railway PostgreSQL.

This script creates the kpi_definitions and kpi_query_history tables
for storing dynamically generated KPI queries.
"""

import logging
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.models.learning_models import Base, KpiDefinition, KpiQueryHistory
from src.utils.learning_db_utils import get_db_manager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_kpi_tables():
    """Create KPI tables in the database."""
    try:
        # Get database manager
        db_manager = get_db_manager()
        engine = db_manager.engine
        
        logger.info("🔨 Creating KPI tables...")
        
        # Create tables
        Base.metadata.create_all(
            engine,
            tables=[
                KpiDefinition.__table__,
                KpiQueryHistory.__table__
            ]
        )
        
        logger.info("✅ KPI tables created successfully!")
        
        # Verify tables exist
        with db_manager.get_session() as session:
            # Check if we can query the tables
            kpi_count = session.query(KpiDefinition).count()
            history_count = session.query(KpiQueryHistory).count()
            
            logger.info(f"📊 KPI definitions: {kpi_count}")
            logger.info(f"📊 Query history entries: {history_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating KPI tables: {e}")
        return False


def populate_initial_kpis():
    """Populate initial KPI definitions from JSON."""
    try:
        import json
        
        # Load KPIs from JSON
        json_path = Path("src/config/setores/cambio/kpis-exchange-json.json")
        with open(json_path, 'r', encoding='utf-8') as f:
            kpis_data = json.load(f)
        
        db_manager = get_db_manager()
        
        with db_manager.get_session() as session:
            # Track KPIs to add
            kpis_added = 0
            
            # Process each category
            for category in kpis_data.get('categories', []):
                category_name = category.get('name', 'Unknown')
                
                for kpi in category.get('kpis', []):
                    # Check if KPI already exists
                    existing = session.query(KpiDefinition).filter_by(
                        id=kpi['id']
                    ).first()
                    
                    if not existing:
                        # Create new KPI definition
                        kpi_def = KpiDefinition(
                            id=kpi['id'],
                            name=kpi.get('name', kpi['id']),
                            description=kpi.get('description', ''),
                            category=category.get('id', 'general'),
                            unit=kpi.get('unit', ''),
                            format_type=kpi.get('format_type', 'number'),
                            sql_query=kpi.get('sql_query'),  # May be None initially
                            is_priority=kpi.get('is_priority', False),
                            sector='cambio'
                        )
                        session.add(kpi_def)
                        kpis_added += 1
                        logger.info(f"➕ Added KPI: {kpi['id']}")
            
            session.commit()
            logger.info(f"✅ Added {kpis_added} KPI definitions to database")
            
    except Exception as e:
        logger.error(f"❌ Error populating KPIs: {e}")


if __name__ == "__main__":
    # Create tables
    if create_kpi_tables():
        # Populate initial data
        populate_initial_kpis()
    else:
        logger.error("Failed to create tables")
        sys.exit(1)