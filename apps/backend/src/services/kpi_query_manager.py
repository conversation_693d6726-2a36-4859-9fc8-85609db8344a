"""
KPI Query Manager - Sistema de Gerenciamento de Queries Dinâmicas
================================================================

Este serviço gerencia queries SQL dinâmicas para KPIs, permitindo:
- Carregamento de queries do JSON de configuração
- Atualização de queries via linguagem natural
- Versionamento e histórico de modificações
- Cache inteligente para performance
- Integração com o sistema multi-agentes existente
"""

import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
import asyncio

from ..agents.business_analyst import BusinessAnalyst
from ..agents.query_validator import QueryValidator
from ..tools.llm_provider import LLMProvider
from ..caching.hierarchical_cache import get_hierarchical_cache

logger = logging.getLogger(__name__)


class KpiQueryManager:
    """
    Gerencia queries dinâmicas de KPIs com suporte completo para:
    - Carregamento e cache de queries
    - Edição via linguagem natural
    - Validação e testes
    - Versionamento
    """
    
    def __init__(self, sector: str = "cambio"):
        """
        Inicializa o gerenciador de queries KPI.
        
        Args:
            sector: Setor para localizar o arquivo de KPIs
        """
        self.sector = sector
        self.kpi_json_path = Path(f"src/config/setores/{sector}/kpis-exchange-json.json")
        self._query_cache = {}
        self._kpis_data = None
        self.cache = get_hierarchical_cache()
        self._generation_in_progress = set()  # KPIs sendo gerados
        self._generation_queue = asyncio.Queue()  # Fila de geração
        
        # Inicializar agentes
        self._init_agents()
        
        # Iniciar worker de geração em background
        self._start_generation_worker()
    
    def _init_agents(self):
        """Inicializa os agentes necessários."""
        try:
            # Usar os agentes existentes do sistema
            self.business_analyst = BusinessAnalyst()
            self.query_validator = QueryValidator()
            self.llm_provider = LLMProvider()
            logger.info("✅ Agentes inicializados para KPI Query Manager")
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar agentes: {e}")
            # Fallback para versões simplificadas se necessário
            self.business_analyst = None
            self.query_validator = None
    
    def _load_kpis_json(self) -> Dict[str, Any]:
        """Carrega o JSON de KPIs do disco."""
        if self._kpis_data is None:
            try:
                with open(self.kpi_json_path, 'r', encoding='utf-8') as f:
                    self._kpis_data = json.load(f)
                logger.info(f"✅ KPIs carregados de: {self.kpi_json_path}")
            except Exception as e:
                logger.error(f"❌ Erro ao carregar KPIs: {e}")
                self._kpis_data = {"categories": []}
        
        return self._kpis_data
    
    def _save_kpis_json(self, create_backup: bool = True):
        """
        Salva o JSON de KPIs no disco.
        
        Args:
            create_backup: Se deve criar backup antes de salvar
        """
        if create_backup:
            backup_path = self.kpi_json_path.with_suffix('.json.backup')
            try:
                if self.kpi_json_path.exists():
                    with open(backup_path, 'w', encoding='utf-8') as f:
                        current_data = self._load_kpis_json()
                        json.dump(current_data, f, indent=2, ensure_ascii=False)
                    logger.info(f"📦 Backup criado: {backup_path}")
            except Exception as e:
                logger.warning(f"⚠️ Não foi possível criar backup: {e}")
        
        # Salvar dados atualizados
        try:
            with open(self.kpi_json_path, 'w', encoding='utf-8') as f:
                json.dump(self._kpis_data, f, indent=2, ensure_ascii=False)
            logger.info("✅ KPIs salvos com sucesso")
            
            # Limpar cache após salvar
            self._invalidate_cache()
        except Exception as e:
            logger.error(f"❌ Erro ao salvar KPIs: {e}")
            raise
    
    def _find_kpi_in_json(self, kpi_id: str) -> Optional[Dict[str, Any]]:
        """Encontra um KPI específico no JSON."""
        kpis_data = self._load_kpis_json()
        
        for category in kpis_data.get('categories', []):
            for kpi in category.get('kpis', []):
                if kpi['id'] == kpi_id:
                    return kpi
        
        return None
    
    def _invalidate_cache(self, kpi_id: Optional[str] = None):
        """
        Invalida o cache de queries.
        
        Args:
            kpi_id: ID específico para invalidar (None para limpar tudo)
        """
        if kpi_id:
            self._query_cache.pop(kpi_id, None)
            # Invalidar também no cache hierárquico
            cache_key = f"kpi_query:{self.sector}:{kpi_id}"
            self.cache.delete(cache_key)
        else:
            self._query_cache.clear()
            # Invalidar padrão no cache hierárquico
            self.cache.delete_pattern(f"kpi_query:{self.sector}:*")
    
    def get_kpi_query(self, kpi_id: str) -> Optional[str]:
        """
        Retorna a query SQL para um KPI específico.
        
        Args:
            kpi_id: Identificador do KPI
            
        Returns:
            Query SQL ou None se não encontrada
        """
        # Verificar cache em memória
        if kpi_id in self._query_cache:
            logger.debug(f"🚀 Query {kpi_id} encontrada no cache")
            return self._query_cache[kpi_id]
        
        # Verificar cache hierárquico
        cache_key = f"kpi_query:{self.sector}:{kpi_id}"
        cached_query = self.cache.get(cache_key)
        if cached_query:
            logger.debug(f"🚀 Query {kpi_id} encontrada no cache hierárquico")
            self._query_cache[kpi_id] = cached_query
            return cached_query
        
        # Buscar no JSON
        kpi_data = self._find_kpi_in_json(kpi_id)
        if kpi_data and kpi_data.get('sql_query'):
            query = kpi_data['sql_query']
            
            # Adicionar aos caches
            self._query_cache[kpi_id] = query
            self.cache.set(cache_key, query, ttl=3600)  # Cache por 1 hora
            
            logger.info(f"✅ Query encontrada para KPI {kpi_id}")
            return query
        
        # Se não tem query, marcar para geração assíncrona
        logger.info(f"📝 KPI {kpi_id} não tem query, será gerada sob demanda")
        self._mark_for_generation(kpi_id)
        return None
    
    def get_kpi_metadata(self, kpi_id: str) -> Optional[Dict[str, Any]]:
        """
        Retorna metadados da query de um KPI.
        
        Args:
            kpi_id: Identificador do KPI
            
        Returns:
            Metadados ou None se não encontrados
        """
        kpi_data = self._find_kpi_in_json(kpi_id)
        if kpi_data:
            return kpi_data.get('query_metadata', {})
        return None
    
    async def update_kpi_via_natural_language(
        self, 
        kpi_id: str, 
        instruction: str,
        user_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Atualiza uma query KPI usando linguagem natural.
        
        Args:
            kpi_id: Identificador do KPI
            instruction: Instrução em linguagem natural
            user_context: Contexto adicional do usuário
            
        Returns:
            Resultado da atualização
        """
        logger.info(f"🔄 Atualizando KPI {kpi_id} via NL: {instruction}")
        
        # Obter query atual
        current_query = self.get_kpi_query(kpi_id)
        if not current_query:
            return {
                "success": False,
                "error": f"KPI {kpi_id} não possui query definida"
            }
        
        # Obter informações do KPI
        kpi_data = self._find_kpi_in_json(kpi_id)
        if not kpi_data:
            return {
                "success": False,
                "error": f"KPI {kpi_id} não encontrado"
            }
        
        try:
            # Usar BusinessAnalyst para processar a instrução
            if self.business_analyst:
                # Preparar contexto para o BusinessAnalyst
                context = {
                    "current_query": current_query,
                    "kpi_name": kpi_data.get('name', kpi_id),
                    "kpi_description": kpi_data.get('description', ''),
                    "instruction": instruction,
                    "user_context": user_context or {}
                }
                
                # Processar com o agente
                result = await self._process_with_business_analyst(context)
                
                if result.get('success'):
                    updated_query = result.get('updated_query')
                    
                    # Validar a nova query
                    validation = await self._validate_query(updated_query, kpi_id)
                    
                    if validation.get('is_valid'):
                        # Salvar a query atualizada
                        await self._save_updated_query(
                            kpi_id, 
                            updated_query, 
                            instruction,
                            validation
                        )
                        
                        return {
                            "success": True,
                            "kpi_id": kpi_id,
                            "new_query": updated_query,
                            "previous_query": current_query,
                            "instruction": instruction,
                            "confidence": result.get('confidence', 0.85)
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"Query inválida: {validation.get('message', 'Erro de validação')}"
                        }
                else:
                    return {
                        "success": False,
                        "error": result.get('error', 'Erro ao processar instrução')
                    }
            else:
                # Fallback simples se BusinessAnalyst não estiver disponível
                return await self._simple_query_update(
                    kpi_id, 
                    current_query, 
                    instruction
                )
                
        except Exception as e:
            logger.error(f"❌ Erro ao atualizar KPI via NL: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _process_with_business_analyst(
        self, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Processa a instrução usando o BusinessAnalyst.
        
        Args:
            context: Contexto com query atual e instrução
            
        Returns:
            Resultado do processamento
        """
        try:
            # Formular pergunta para o BusinessAnalyst
            prompt = f"""
            Você precisa modificar uma query SQL baseada na seguinte instrução do usuário.
            
            Query atual:
            {context['current_query']}
            
            KPI: {context['kpi_name']}
            Descrição: {context['kpi_description']}
            
            Instrução do usuário: {context['instruction']}
            
            Retorne APENAS a query SQL modificada, sem explicações.
            Mantenha a estrutura geral e os parâmetros (:client_id) da query original.
            """
            
            # Usar o BusinessAnalyst (simplificado por ora)
            # TODO: Integrar com o método real do BusinessAnalyst quando disponível
            response = await self.llm_provider.generate_response(
                prompt,
                temperature=0.1,
                max_tokens=500
            )
            
            # Extrair query da resposta
            updated_query = self._extract_sql_from_response(response)
            
            return {
                "success": True,
                "updated_query": updated_query,
                "confidence": 0.85
            }
            
        except Exception as e:
            logger.error(f"❌ Erro no BusinessAnalyst: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _validate_query(
        self, 
        query: str, 
        kpi_id: str
    ) -> Dict[str, Any]:
        """
        Valida uma query SQL.
        
        Args:
            query: Query SQL a validar
            kpi_id: ID do KPI para contexto
            
        Returns:
            Resultado da validação
        """
        try:
            if self.query_validator:
                # Usar QueryValidator existente
                validation_result = await self.query_validator.validate(
                    query,
                    context={"kpi_id": kpi_id}
                )
                return validation_result
            else:
                # Validação básica
                return self._basic_query_validation(query)
                
        except Exception as e:
            logger.error(f"❌ Erro na validação: {e}")
            return {
                "is_valid": False,
                "message": str(e)
            }
    
    def _basic_query_validation(self, query: str) -> Dict[str, Any]:
        """Validação básica de query SQL."""
        # Verificações simples
        query_lower = query.lower()
        
        # Deve ser SELECT
        if not query_lower.strip().startswith('select'):
            return {
                "is_valid": False,
                "message": "Query deve começar com SELECT"
            }
        
        # Deve ter :client_id
        if ':client_id' not in query:
            return {
                "is_valid": False,
                "message": "Query deve incluir parâmetro :client_id"
            }
        
        # Não pode ter comandos perigosos
        dangerous_keywords = ['drop', 'delete', 'truncate', 'update', 'insert']
        for keyword in dangerous_keywords:
            if keyword in query_lower:
                return {
                    "is_valid": False,
                    "message": f"Query não pode conter {keyword.upper()}"
                }
        
        return {
            "is_valid": True,
            "message": "Validação básica passou"
        }
    
    async def _save_updated_query(
        self,
        kpi_id: str,
        new_query: str,
        instruction: str,
        validation: Dict[str, Any]
    ):
        """
        Salva a query atualizada no JSON.
        
        Args:
            kpi_id: ID do KPI
            new_query: Nova query SQL
            instruction: Instrução que gerou a mudança
            validation: Resultado da validação
        """
        # Encontrar e atualizar KPI
        kpi_data = self._find_kpi_in_json(kpi_id)
        if not kpi_data:
            raise ValueError(f"KPI {kpi_id} não encontrado")
        
        # Preservar query anterior no histórico
        current_query = kpi_data.get('sql_query')
        current_metadata = kpi_data.get('query_metadata', {})
        
        # Criar entrada no histórico
        history_entry = {
            "version": current_metadata.get('version', 1),
            "query": current_query,
            "replaced_at": datetime.now().isoformat(),
            "replaced_by": instruction
        }
        
        # Atualizar metadados
        modification_history = current_metadata.get('modification_history', [])
        modification_history.append(history_entry)
        
        # Atualizar KPI
        kpi_data['sql_query'] = new_query
        kpi_data['query_metadata'] = {
            'generated_at': current_metadata.get('generated_at'),
            'generated_by': 'natural_language_update',
            'confidence': validation.get('confidence', 0.85),
            'validation_passed': validation.get('is_valid', True),
            'version': current_metadata.get('version', 1) + 1,
            'last_modified': datetime.now().isoformat(),
            'last_modification': instruction,
            'modification_history': modification_history[-5:]  # Manter últimas 5
        }
        
        # Salvar no JSON
        self._save_kpis_json()
        
        # Invalidar cache
        self._invalidate_cache(kpi_id)
        
        logger.info(f"✅ Query do KPI {kpi_id} atualizada com sucesso")
    
    def _extract_sql_from_response(self, response: str) -> str:
        """Extrai SQL de uma resposta LLM."""
        # Remover markdown se houver
        if '```sql' in response:
            start = response.find('```sql') + 6
            end = response.find('```', start)
            if end > start:
                return response[start:end].strip()
        elif '```' in response:
            start = response.find('```') + 3
            end = response.find('```', start)
            if end > start:
                return response[start:end].strip()
        
        # Retornar resposta limpa
        return response.strip()
    
    async def _simple_query_update(
        self,
        kpi_id: str,
        current_query: str,
        instruction: str
    ) -> Dict[str, Any]:
        """
        Atualização simples de query (fallback).
        
        Args:
            kpi_id: ID do KPI
            current_query: Query atual
            instruction: Instrução do usuário
            
        Returns:
            Resultado da atualização
        """
        # Implementar lógica simples para casos comuns
        instruction_lower = instruction.lower()
        updated_query = current_query
        
        # Detectar mudanças de período
        if 'meses' in instruction_lower or 'months' in instruction_lower:
            # Extrair número
            import re
            numbers = re.findall(r'\d+', instruction)
            if numbers:
                months = numbers[0]
                # Substituir período na query
                updated_query = re.sub(
                    r"INTERVAL '\d+ months?'",
                    f"INTERVAL '{months} months'",
                    current_query
                )
        
        # Detectar mudanças de agregação
        elif 'média' in instruction_lower or 'average' in instruction_lower:
            updated_query = current_query.replace('SUM(', 'AVG(')
        elif 'total' in instruction_lower or 'soma' in instruction_lower:
            updated_query = current_query.replace('AVG(', 'SUM(')
        
        # Se houve mudança
        if updated_query != current_query:
            # Validar
            validation = await self._validate_query(updated_query, kpi_id)
            
            if validation.get('is_valid'):
                await self._save_updated_query(
                    kpi_id,
                    updated_query,
                    instruction,
                    validation
                )
                
                return {
                    "success": True,
                    "kpi_id": kpi_id,
                    "new_query": updated_query,
                    "previous_query": current_query,
                    "instruction": instruction,
                    "confidence": 0.7  # Menor confiança no fallback
                }
        
        return {
            "success": False,
            "error": "Não foi possível processar a instrução automaticamente"
        }
    
    def get_all_kpis_with_queries(self) -> List[Dict[str, Any]]:
        """
        Retorna todos os KPIs que possuem queries definidas.
        
        Returns:
            Lista de KPIs com suas queries
        """
        kpis_with_queries = []
        kpis_data = self._load_kpis_json()
        
        for category in kpis_data.get('categories', []):
            for kpi in category.get('kpis', []):
                if kpi.get('sql_query'):
                    kpis_with_queries.append({
                        'id': kpi['id'],
                        'name': kpi['name'],
                        'category': category['name'],
                        'has_query': True,
                        'query_version': kpi.get('query_metadata', {}).get('version', 1),
                        'last_modified': kpi.get('query_metadata', {}).get('last_modified')
                    })
        
        return kpis_with_queries
    
    def rollback_query(self, kpi_id: str, version: Optional[int] = None) -> Dict[str, Any]:
        """
        Reverte uma query para uma versão anterior.
        
        Args:
            kpi_id: ID do KPI
            version: Versão para reverter (None para anterior)
            
        Returns:
            Resultado do rollback
        """
        kpi_data = self._find_kpi_in_json(kpi_id)
        if not kpi_data:
            return {
                "success": False,
                "error": f"KPI {kpi_id} não encontrado"
            }
        
        metadata = kpi_data.get('query_metadata', {})
        history = metadata.get('modification_history', [])
        
        if not history:
            return {
                "success": False,
                "error": "Sem histórico de modificações"
            }
        
        # Encontrar versão desejada
        if version is None:
            # Reverter para versão anterior
            target = history[-1]
        else:
            # Procurar versão específica
            target = next((h for h in history if h['version'] == version), None)
            if not target:
                return {
                    "success": False,
                    "error": f"Versão {version} não encontrada"
                }
        
        # Aplicar rollback
        old_query = kpi_data['sql_query']
        kpi_data['sql_query'] = target['query']
        
        # Atualizar metadados
        metadata['version'] += 1
        metadata['last_modified'] = datetime.now().isoformat()
        metadata['last_modification'] = f"Rollback para versão {target['version']}"
        
        # Adicionar ao histórico
        history.append({
            "version": metadata['version'] - 1,
            "query": old_query,
            "replaced_at": datetime.now().isoformat(),
            "replaced_by": f"Rollback para v{target['version']}"
        })
        
        # Salvar
        self._save_kpis_json()
        self._invalidate_cache(kpi_id)
        
        logger.info(f"✅ KPI {kpi_id} revertido para versão {target['version']}")
        
        return {
            "success": True,
            "kpi_id": kpi_id,
            "reverted_to_version": target['version'],
            "new_query": target['query']
        }
    
    def _mark_for_generation(self, kpi_id: str):
        """
        Marca um KPI para geração assíncrona de query.
        
        Args:
            kpi_id: ID do KPI
        """
        if kpi_id not in self._generation_in_progress:
            self._generation_in_progress.add(kpi_id)
            # Adicionar à fila de geração
            asyncio.create_task(self._generation_queue.put(kpi_id))
            logger.info(f"🔄 KPI {kpi_id} adicionado à fila de geração")
    
    def _start_generation_worker(self):
        """Inicia worker de geração em background."""
        asyncio.create_task(self._generation_worker())
    
    async def _generation_worker(self):
        """Worker que processa a fila de geração de queries."""
        logger.info("🚀 Worker de geração de queries KPI iniciado")
        
        while True:
            try:
                # Pegar próximo KPI da fila
                kpi_id = await self._generation_queue.get()
                
                # Gerar query usando o pipeline
                logger.info(f"🔧 Gerando query para KPI {kpi_id}")
                success = await self._generate_kpi_query(kpi_id)
                
                if success:
                    logger.info(f"✅ Query gerada com sucesso para {kpi_id}")
                else:
                    logger.error(f"❌ Falha ao gerar query para {kpi_id}")
                
                # Remover da lista de em progresso
                self._generation_in_progress.discard(kpi_id)
                
            except Exception as e:
                logger.error(f"❌ Erro no worker de geração: {e}")
                await asyncio.sleep(5)  # Aguardar antes de continuar
    
    async def _generate_kpi_query(self, kpi_id: str) -> bool:
        """
        Gera query para um KPI usando o pipeline multi-agentes.
        
        Args:
            kpi_id: ID do KPI
            
        Returns:
            True se gerou com sucesso
        """
        try:
            # Importar aqui para evitar dependência circular
            from ..interfaces.api import process_question_internal
            
            # Obter definição do KPI
            kpi_data = self._find_kpi_in_json(kpi_id)
            if not kpi_data:
                logger.error(f"KPI {kpi_id} não encontrado no JSON")
                return False
            
            # Formular pergunta baseada no KPI
            question = self._formulate_kpi_question(kpi_data)
            
            # Usar o pipeline para gerar a query
            result = await process_question_internal(
                question=question,
                client_id="L2M",
                sector="cambio",
                thread_id=f"kpi_gen_{kpi_id}",
                context={
                    "mode": "kpi_generation",
                    "kpi_id": kpi_id,
                    "require_single_value": True
                }
            )
            
            # Extrair e salvar query
            if result and result.get('sql_query'):
                query = result['sql_query']
                confidence = result.get('confidence_score', 0.0)
                
                # Salvar no JSON
                await self._save_generated_query(
                    kpi_id,
                    query,
                    {
                        'generated_by': 'pipeline_on_demand',
                        'confidence': confidence,
                        'validation_passed': result.get('validation', {}).get('is_valid', True)
                    }
                )
                
                return True
            else:
                logger.error(f"Pipeline não retornou query para {kpi_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao gerar query para {kpi_id}: {e}")
            return False
    
    def _formulate_kpi_question(self, kpi_data: Dict[str, Any]) -> str:
        """
        Formula uma pergunta natural baseada na definição do KPI.
        
        Args:
            kpi_data: Dados do KPI
            
        Returns:
            Pergunta em linguagem natural
        """
        # Mapeamento de IDs para perguntas otimizadas
        kpi_questions = {
            "total_volume": "Qual o volume total em valor de todas as operações de câmbio dos últimos 12 meses? Retorne apenas o valor total somado.",
            "average_ticket": "Qual o valor médio (ticket médio) das operações de câmbio dos últimos 12 meses? Retorne apenas o valor médio.",
            "average_spread": "Qual o spread médio percentual das operações de câmbio dos últimos 6 meses? Calcule a diferença entre taxa aplicada e taxa base.",
            "conversion_rate": "Qual a taxa de conversão (percentual de operações concluídas com sucesso) dos últimos 12 meses? Considere status 4 e 7 como concluídas.",
            "retention_rate": "Qual a taxa de retenção de clientes comparando o ano atual com o ano anterior? Calcule quantos clientes continuaram operando.",
            "operations_per_analyst": "Qual a média de operações processadas por analista no último mês? Divida o total de operações pelo número de analistas únicos."
        }
        
        # Usar pergunta específica se disponível
        kpi_id = kpi_data.get('id')
        if kpi_id in kpi_questions:
            return kpi_questions[kpi_id]
        
        # Fallback genérico baseado na descrição
        name = kpi_data.get('name', kpi_id)
        description = kpi_data.get('description', '')
        
        return f"Como calcular o KPI '{name}'? {description}. Retorne apenas o valor numérico."
    
    async def _save_generated_query(
        self,
        kpi_id: str,
        query: str,
        metadata: Dict[str, Any]
    ):
        """
        Salva a query gerada no JSON.
        
        Args:
            kpi_id: ID do KPI
            query: Query SQL gerada
            metadata: Metadados da geração
        """
        # Encontrar KPI no JSON
        kpi_data = self._find_kpi_in_json(kpi_id)
        if not kpi_data:
            return
        
        # Atualizar com query gerada
        kpi_data['sql_query'] = query
        kpi_data['query_metadata'] = {
            'generated_at': datetime.now().isoformat(),
            'generated_by': metadata.get('generated_by', 'pipeline'),
            'confidence': metadata.get('confidence', 0.0),
            'validation_passed': metadata.get('validation_passed', True),
            'version': 1,
            'last_modified': None,
            'modification_history': []
        }
        
        # Salvar JSON
        self._save_kpis_json(create_backup=False)  # Sem backup para geração automática
        
        # Invalidar cache para forçar recarga
        self._invalidate_cache(kpi_id)
        
        logger.info(f"✅ Query salva para KPI {kpi_id}")