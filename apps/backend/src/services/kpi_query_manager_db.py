"""
KPI Query Manager with PostgreSQL Backend
========================================

Gerencia queries dinâmicas de KPIs usando PostgreSQL do Railway
em vez de arquivos JSON locais.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import asyncio

from sqlalchemy.orm import Session
from sqlalchemy import desc

from ..models.learning_models import KpiDefinition, KpiQueryHistory
from ..utils.learning_db_utils import get_db_manager
from ..agents.business_analyst import BusinessAnalystAgent
from ..agents.query_validator import QueryValidatorAgent
from ..caching.hierarchical_cache import get_hierarchical_cache

logger = logging.getLogger(__name__)


class KpiQueryManagerDB:
    """
    Gerencia queries dinâmicas de KPIs usando PostgreSQL.
    
    Vantagens sobre JSON:
    - Concorrência segura
    - Versionamento automático
    - Queries SQL para análise
    - Backup automático no Railway
    - Escalabilidade
    """
    
    def __init__(self, sector: str = "cambio"):
        """
        Inicializa o gerenciador de queries KPI.
        
        Args:
            sector: Setor para filtrar KPIs
        """
        self.sector = sector
        self.db_manager = get_db_manager()
        self.cache = get_hierarchical_cache()
        self._generation_in_progress = set()
        
        # Only initialize async queue if event loop is running
        try:
            asyncio.get_running_loop()
            self._generation_queue = asyncio.Queue()
            self._has_event_loop = True
        except RuntimeError:
            self._generation_queue = None
            self._has_event_loop = False
            logger.warning("⚠️ No event loop running, background worker disabled")
        
        # Inicializar agentes
        self._init_agents()
        
        # Iniciar worker de geração em background apenas se houver event loop
        if self._has_event_loop:
            self._start_generation_worker()
    
    def _init_agents(self):
        """Inicializa os agentes necessários."""
        try:
            self.business_analyst = BusinessAnalystAgent(self.sector, "L2M")
            self.query_validator = QueryValidatorAgent()
            logger.info("✅ Agentes inicializados para KPI Query Manager DB")
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar agentes: {e}")
            self.business_analyst = None
            self.query_validator = None
    
    def get_kpi_query(self, kpi_id: str, client_id: Optional[str] = None) -> Optional[str]:
        """
        Retorna a query SQL para um KPI específico.
        
        Args:
            kpi_id: Identificador do KPI
            client_id: Cliente específico (opcional)
            
        Returns:
            Query SQL ou None se não encontrada
        """
        # Verificar cache
        cache_key = f"kpi_query:{self.sector}:{kpi_id}:{client_id or 'default'}"
        cached_query = self.cache.get(cache_key)
        if cached_query:
            logger.debug(f"🚀 Query {kpi_id} encontrada no cache")
            return cached_query
        
        # Buscar no PostgreSQL
        with self.db_manager.get_session() as session:
            kpi = session.query(KpiDefinition).filter(
                KpiDefinition.id == kpi_id,
                KpiDefinition.sector == self.sector,
                KpiDefinition.is_active == True
            ).first()
            
            if kpi and kpi.sql_query:
                # Adicionar ao cache
                self.cache.set(cache_key, kpi.sql_query, ttl=3600)
                
                # Atualizar última execução
                kpi.last_execution = datetime.utcnow()
                session.commit()
                
                logger.info(f"✅ Query encontrada para KPI {kpi_id}")
                return kpi.sql_query
        
        # Se não tem query, marcar para geração
        logger.info(f"📝 KPI {kpi_id} não tem query, será gerada sob demanda")
        self._mark_for_generation(kpi_id, client_id)
        return None
    
    def get_kpi_metadata(self, kpi_id: str) -> Optional[Dict[str, Any]]:
        """
        Retorna metadados completos de um KPI.
        
        Args:
            kpi_id: Identificador do KPI
            
        Returns:
            Metadados ou None se não encontrado
        """
        with self.db_manager.get_session() as session:
            kpi = session.query(KpiDefinition).filter_by(id=kpi_id).first()
            
            if kpi:
                return {
                    'id': kpi.id,
                    'name': kpi.name,
                    'description': kpi.description,
                    'category': kpi.category,
                    'sql_query': kpi.sql_query,
                    'query_version': kpi.query_version,
                    'query_confidence': kpi.query_confidence,
                    'generated_at': kpi.generated_at.isoformat() if kpi.generated_at else None,
                    'last_modified': kpi.last_modified.isoformat() if kpi.last_modified else None,
                    'is_priority': kpi.is_priority,
                    'avg_execution_time': kpi.avg_execution_time
                }
        
        return None
    
    async def update_kpi_via_natural_language(
        self, 
        kpi_id: str, 
        instruction: str,
        user_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Atualiza uma query KPI usando linguagem natural.
        
        Args:
            kpi_id: Identificador do KPI
            instruction: Instrução em linguagem natural
            user_context: Contexto adicional do usuário
            
        Returns:
            Resultado da atualização
        """
        logger.info(f"🔄 Atualizando KPI {kpi_id} via NL: {instruction}")
        
        # Obter KPI do banco
        with self.db_manager.get_session() as session:
            kpi = session.query(KpiDefinition).filter_by(id=kpi_id).first()
            
            if not kpi or not kpi.sql_query:
                return {
                    "success": False,
                    "error": f"KPI {kpi_id} não encontrado ou sem query"
                }
            
            current_query = kpi.sql_query
            
            try:
                # Processar com BusinessAnalyst
                if self.business_analyst:
                    context = {
                        "current_query": current_query,
                        "kpi_name": kpi.name,
                        "kpi_description": kpi.description,
                        "instruction": instruction,
                        "user_context": user_context or {}
                    }
                    
                    result = await self._process_with_business_analyst(context)
                    
                    if result.get('success'):
                        updated_query = result.get('updated_query')
                        
                        # Validar nova query
                        validation = await self._validate_query(updated_query, kpi_id)
                        
                        if validation.get('is_valid'):
                            # Salvar histórico
                            history = KpiQueryHistory(
                                kpi_id=kpi_id,
                                version=kpi.query_version,
                                sql_query=current_query,
                                change_type='nl_edit',
                                change_description=instruction,
                                changed_by=user_context.get('user_id', 'system')
                            )
                            session.add(history)
                            
                            # Atualizar KPI
                            kpi.sql_query = updated_query
                            kpi.query_version += 1
                            kpi.last_modified = datetime.utcnow()
                            kpi.last_modified_by = f"nl_edit:{user_context.get('user_id', 'system')}"
                            
                            session.commit()
                            
                            # Invalidar cache
                            self._invalidate_cache(kpi_id)
                            
                            return {
                                "success": True,
                                "kpi_id": kpi_id,
                                "new_query": updated_query,
                                "previous_query": current_query,
                                "version": kpi.query_version,
                                "confidence": result.get('confidence', 0.85)
                            }
                        else:
                            return {
                                "success": False,
                                "error": f"Query inválida: {validation.get('message')}"
                            }
                    else:
                        return {
                            "success": False,
                            "error": result.get('error', 'Erro ao processar')
                        }
                else:
                    return {
                        "success": False,
                        "error": "BusinessAnalyst não disponível"
                    }
                    
            except Exception as e:
                logger.error(f"❌ Erro ao atualizar KPI: {e}")
                return {
                    "success": False,
                    "error": str(e)
                }
    
    def get_all_kpis(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """
        Retorna todos os KPIs do setor.
        
        Args:
            active_only: Apenas KPIs ativos
            
        Returns:
            Lista de KPIs
        """
        with self.db_manager.get_session() as session:
            query = session.query(KpiDefinition).filter(
                KpiDefinition.sector == self.sector
            )
            
            if active_only:
                query = query.filter(KpiDefinition.is_active == True)
            
            kpis = query.order_by(KpiDefinition.category, KpiDefinition.name).all()
            
            return [{
                'id': kpi.id,
                'name': kpi.name,
                'category': kpi.category,
                'has_query': bool(kpi.sql_query),
                'is_priority': kpi.is_priority,
                'query_version': kpi.query_version,
                'last_modified': kpi.last_modified.isoformat() if kpi.last_modified else None
            } for kpi in kpis]
    
    def rollback_query(self, kpi_id: str, version: Optional[int] = None) -> Dict[str, Any]:
        """
        Reverte uma query para versão anterior.
        
        Args:
            kpi_id: ID do KPI
            version: Versão específica (None = anterior)
            
        Returns:
            Resultado do rollback
        """
        with self.db_manager.get_session() as session:
            kpi = session.query(KpiDefinition).filter_by(id=kpi_id).first()
            
            if not kpi:
                return {
                    "success": False,
                    "error": f"KPI {kpi_id} não encontrado"
                }
            
            # Buscar histórico
            history_query = session.query(KpiQueryHistory).filter_by(
                kpi_id=kpi_id
            ).order_by(desc(KpiQueryHistory.version))
            
            if version is not None:
                target = history_query.filter_by(version=version).first()
            else:
                target = history_query.first()
            
            if not target:
                return {
                    "success": False,
                    "error": "Sem histórico disponível"
                }
            
            # Salvar estado atual no histórico
            current_history = KpiQueryHistory(
                kpi_id=kpi_id,
                version=kpi.query_version,
                sql_query=kpi.sql_query,
                change_type='rollback',
                change_description=f"Rollback para versão {target.version}",
                changed_by='system'
            )
            session.add(current_history)
            
            # Aplicar rollback
            kpi.sql_query = target.sql_query
            kpi.query_version += 1
            kpi.last_modified = datetime.utcnow()
            kpi.last_modified_by = f"rollback:v{target.version}"
            
            session.commit()
            
            # Invalidar cache
            self._invalidate_cache(kpi_id)
            
            logger.info(f"✅ KPI {kpi_id} revertido para versão {target.version}")
            
            return {
                "success": True,
                "kpi_id": kpi_id,
                "reverted_to_version": target.version,
                "new_query": target.sql_query,
                "current_version": kpi.query_version
            }
    
    def _invalidate_cache(self, kpi_id: str):
        """Invalida cache para um KPI."""
        pattern = f"kpi_query:{self.sector}:{kpi_id}:*"
        self.cache.delete_pattern(pattern)
        logger.debug(f"🗑️ Cache invalidado para {kpi_id}")
    
    def _mark_for_generation(self, kpi_id: str, client_id: Optional[str] = None):
        """Marca KPI para geração assíncrona."""
        if not self._has_event_loop:
            logger.warning(f"⚠️ Cannot generate query for {kpi_id} - no event loop")
            return
            
        key = f"{kpi_id}:{client_id or 'default'}"
        if key not in self._generation_in_progress:
            self._generation_in_progress.add(key)
            asyncio.create_task(self._generation_queue.put((kpi_id, client_id)))
            logger.info(f"🔄 KPI {kpi_id} adicionado à fila de geração")
    
    def _start_generation_worker(self):
        """Inicia worker de geração em background."""
        asyncio.create_task(self._generation_worker())
    
    async def _generation_worker(self):
        """Worker que processa a fila de geração."""
        logger.info("🚀 Worker de geração de queries KPI (DB) iniciado")
        
        while True:
            try:
                kpi_id, client_id = await self._generation_queue.get()
                
                logger.info(f"🔧 Gerando query para KPI {kpi_id}")
                success = await self._generate_kpi_query(kpi_id, client_id)
                
                if success:
                    logger.info(f"✅ Query gerada com sucesso para {kpi_id}")
                else:
                    logger.error(f"❌ Falha ao gerar query para {kpi_id}")
                
                key = f"{kpi_id}:{client_id or 'default'}"
                self._generation_in_progress.discard(key)
                
            except Exception as e:
                logger.error(f"❌ Erro no worker: {e}")
                await asyncio.sleep(5)
    
    async def _generate_kpi_query(self, kpi_id: str, client_id: Optional[str] = None) -> bool:
        """
        Gera query para um KPI usando o pipeline.
        
        Args:
            kpi_id: ID do KPI
            client_id: Cliente específico
            
        Returns:
            True se gerou com sucesso
        """
        try:
            from ..interfaces.api import process_question_internal
            
            # Obter KPI do banco
            with self.db_manager.get_session() as session:
                kpi = session.query(KpiDefinition).filter_by(id=kpi_id).first()
                
                if not kpi:
                    logger.error(f"KPI {kpi_id} não encontrado")
                    return False
                
                # Formular pergunta
                question = self._formulate_kpi_question({
                    'id': kpi.id,
                    'name': kpi.name,
                    'description': kpi.description,
                    'unit': kpi.unit
                })
                
                # Usar pipeline
                result = await process_question_internal(
                    question=question,
                    client_id=client_id or "L2M",
                    sector=self.sector,
                    thread_id=f"kpi_gen_{kpi_id}",
                    context={
                        "mode": "kpi_generation",
                        "kpi_id": kpi_id,
                        "require_single_value": True
                    }
                )
                
                if result and result.get('sql_query'):
                    # Atualizar no banco
                    kpi.sql_query = result['sql_query']
                    kpi.query_confidence = result.get('confidence_score', 0.0)
                    kpi.generated_at = datetime.utcnow()
                    kpi.generated_by = 'pipeline_on_demand'
                    kpi.validation_passed = result.get('validation', {}).get('is_valid', True)
                    
                    session.commit()
                    
                    # Invalidar cache
                    self._invalidate_cache(kpi_id)
                    
                    return True
                else:
                    logger.error(f"Pipeline não retornou query para {kpi_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Erro ao gerar query: {e}")
            return False
    
    def _formulate_kpi_question(self, kpi_data: Dict[str, Any]) -> str:
        """Formula pergunta natural para KPI."""
        kpi_questions = {
            "total_volume": "Qual o volume total em valor de todas as operações de câmbio dos últimos 12 meses? Retorne apenas o valor total somado.",
            "average_ticket": "Qual o valor médio (ticket médio) das operações de câmbio dos últimos 12 meses? Retorne apenas o valor médio.",
            "average_spread": "Qual o spread médio percentual das operações de câmbio dos últimos 6 meses? Calcule a diferença entre taxa aplicada e taxa base.",
            "conversion_rate": "Qual a taxa de conversão (percentual de operações concluídas com sucesso) dos últimos 12 meses? Considere status 4 e 7 como concluídas.",
            "retention_rate": "Qual a taxa de retenção de clientes comparando o ano atual com o ano anterior? Calcule quantos clientes continuaram operando.",
            "operations_per_analyst": "Qual a média de operações processadas por analista no último mês? Divida o total de operações pelo número de analistas únicos."
        }
        
        kpi_id = kpi_data.get('id')
        if kpi_id in kpi_questions:
            return kpi_questions[kpi_id]
        
        name = kpi_data.get('name', kpi_id)
        description = kpi_data.get('description', '')
        
        return f"Como calcular o KPI '{name}'? {description}. Retorne apenas o valor numérico."
    
    async def _process_with_business_analyst(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Processa com BusinessAnalyst."""
        try:
            # Use BusinessAnalystAgent diretamente
            state = {
                "question": context['instruction'],
                "sql_query": context['current_query'],
                "context": {
                    "kpi_name": context['kpi_name'],
                    "kpi_description": context.get('kpi_description', ''),
                    "current_query": context['current_query']
                }
            }
            
            # O BusinessAnalyst já tem seu próprio LLM
            response = self.business_analyst.analyze_business_scenario(state)
            
            # Extract the updated query from response
            if 'business_analysis' in response:
                analysis = response['business_analysis']
                # TODO: Parse the business analysis to extract the modified query
                # For now, return the original query
                return {
                    "success": True,
                    "updated_query": context['current_query'],
                    "confidence": 0.85
                }
            
            return {
                "success": False,
                "error": "No analysis generated"
            }
            
        except Exception as e:
            logger.error(f"❌ Erro no BusinessAnalyst: {e}")
            return {"success": False, "error": str(e)}
    
    async def _validate_query(self, query: str, kpi_id: str) -> Dict[str, Any]:
        """Valida query SQL."""
        try:
            if self.query_validator:
                # Create state for QueryValidatorAgent
                state = {
                    "sql_query": query,
                    "context": {"kpi_id": kpi_id}
                }
                result = self.query_validator.validate_query(state)
                
                return {
                    "is_valid": result.get("is_valid", False),
                    "message": result.get("error_message", "OK") if not result.get("is_valid") else "OK"
                }
            else:
                # Validação básica
                query_lower = query.lower()
                
                if not query_lower.strip().startswith('select'):
                    return {"is_valid": False, "message": "Query deve começar com SELECT"}
                
                if ':client_id' not in query:
                    return {"is_valid": False, "message": "Query deve incluir :client_id"}
                
                dangerous = ['drop', 'delete', 'truncate', 'update', 'insert']
                for word in dangerous:
                    if word in query_lower:
                        return {"is_valid": False, "message": f"Query não pode conter {word.upper()}"}
                
                return {"is_valid": True, "message": "OK"}
                
        except Exception as e:
            logger.error(f"❌ Erro na validação: {e}")
            return {"is_valid": False, "message": str(e)}
    
    def _extract_sql_from_response(self, response: str) -> str:
        """Extrai SQL de resposta LLM."""
        if '```sql' in response:
            start = response.find('```sql') + 6
            end = response.find('```', start)
            if end > start:
                return response[start:end].strip()
        elif '```' in response:
            start = response.find('```') + 3
            end = response.find('```', start)
            if end > start:
                return response[start:end].strip()
        
        return response.strip()