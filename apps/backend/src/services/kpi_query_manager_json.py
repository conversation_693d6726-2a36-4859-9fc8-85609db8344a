"""
KPI Query Manager - JSON-based implementation.
Manages dynamic KPI queries using JSON files instead of PostgreSQL.
"""

import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path
from datetime import datetime
import asyncio

logger = logging.getLogger(__name__)


class KpiQueryManagerJSON:
    """
    Manages KPI queries using JSON files.
    Simpler alternative to PostgreSQL-based manager.
    """
    
    def __init__(self, sector: str = "cambio"):
        """Initialize JSON-based KPI query manager."""
        self.sector = sector
        self.config_path = Path(f"src/config/setores/{sector}/kpis-exchange-json.json")
        self.queries_path = Path(f"src/config/setores/{sector}/kpi_queries.json")
        self._kpi_data = self._load_kpi_data()
        self._queries = self._load_queries()
        self._builder = None  # Lazy load when needed
        self._generation_in_progress = set()
        
    def _load_kpi_data(self) -> Dict[str, Any]:
        """Load KPI definitions from JSON."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"✅ Loaded KPI definitions from {self.config_path}")
            return data
        except Exception as e:
            logger.error(f"❌ Error loading KPI definitions: {e}")
            return {"categories": []}
    
    def _load_queries(self) -> Dict[str, str]:
        """Load existing queries from JSON file."""
        if self.queries_path.exists():
            try:
                with open(self.queries_path, 'r', encoding='utf-8') as f:
                    queries = json.load(f)
                logger.info(f"✅ Loaded {len(queries)} queries from {self.queries_path}")
                return queries
            except Exception as e:
                logger.error(f"❌ Error loading queries: {e}")
        return {}
    
    def get_kpi_query(self, kpi_id: str, client_id: Optional[str] = None) -> Optional[str]:
        """
        Get query for a specific KPI.
        First checks JSON queries, then builds dynamically if needed.
        """
        # Check if we have a stored query
        if kpi_id in self._queries:
            return self._queries[kpi_id]
        
        # Build query on-demand
        query = self._build_query_sync(kpi_id)
        
        if query:
            logger.info(f"✅ Query built for {kpi_id}")
            return query
        else:
            logger.warning(f"⚠️ No query found for {kpi_id}")
            return None
    
    def _build_query_sync(self, kpi_id: str) -> Optional[str]:
        """Build query synchronously using the query builder."""
        try:
            # Initialize builder if needed
            if self._builder is None:
                from src.services.kpi_query_builder import KpiQueryBuilder
                self._builder = KpiQueryBuilder(sector=self.sector)
            
            # Find KPI metadata
            kpi_data = self.get_kpi_metadata(kpi_id)
            
            if not kpi_data:
                logger.error(f"❌ KPI {kpi_id} not found in configuration")
                return None
            
            # Build query
            logger.info(f"🔧 Building query for {kpi_id}...")
            query = self._builder.build_query(kpi_data)
            
            if query:
                # Save to JSON
                self.save_query(kpi_id, query, {
                    'generated_by': 'query_builder',
                    'generated_at': datetime.now().isoformat()
                })
                return query
            else:
                return None
                
        except Exception as e:
            logger.error(f"❌ Error building query for {kpi_id}: {e}")
            return None
    
    def _mark_for_generation(self, kpi_id: str):
        """Mark a KPI for asynchronous query generation."""
        if kpi_id not in self._generation_in_progress:
            self._generation_in_progress.add(kpi_id)
            logger.info(f"🔄 KPI {kpi_id} marked for generation")
            
            # Try to start generation if event loop is available
            try:
                loop = asyncio.get_running_loop()
                loop.create_task(self._generate_query_async(kpi_id))
            except RuntimeError:
                logger.warning(f"⚠️ No event loop, cannot generate query for {kpi_id} automatically")
    
    async def _generate_query_async(self, kpi_id: str):
        """Generate query asynchronously for a KPI."""
        try:
            # Initialize generator if needed
            if self._generator is None:
                from src.services.kpi_query_generator import KpiQueryGenerator
                self._generator = KpiQueryGenerator(sector=self.sector)
            
            # Find KPI metadata
            kpi_data = None
            for category in self._kpi_data.get('categories', []):
                for kpi in category.get('kpis', []):
                    if kpi['id'] == kpi_id:
                        kpi_data = kpi
                        break
                if kpi_data:
                    break
            
            if not kpi_data:
                logger.error(f"❌ KPI {kpi_id} not found in configuration")
                return
            
            # Generate query
            logger.info(f"🔧 Generating query for {kpi_id}...")
            query = await self._generator.generate_query_for_kpi(kpi_data)
            
            if query:
                # Save to JSON
                self.save_query(kpi_id, query, {
                    'generated_by': 'pipeline',
                    'confidence': self._generator.get_generated_query(kpi_id).get('confidence', 0)
                })
                logger.info(f"✅ Query generated and saved for {kpi_id}")
            else:
                logger.error(f"❌ Failed to generate query for {kpi_id}")
                
        except Exception as e:
            logger.error(f"❌ Error in async generation for {kpi_id}: {e}")
        finally:
            self._generation_in_progress.discard(kpi_id)
    
    def _get_hardcoded_queries(self) -> Dict[str, str]:
        """No hardcoded queries - all queries are generated dynamically."""
        return {}
    
    def save_query(self, kpi_id: str, query: str, metadata: Optional[Dict[str, Any]] = None):
        """Save a generated query to JSON file."""
        self._queries[kpi_id] = query
        
        # Add metadata if provided
        if metadata:
            if 'metadata' not in self._queries:
                self._queries['metadata'] = {}
            self._queries['metadata'][kpi_id] = {
                'generated_at': datetime.now().isoformat(),
                **metadata
            }
        
        try:
            with open(self.queries_path, 'w', encoding='utf-8') as f:
                json.dump(self._queries, f, indent=2, ensure_ascii=False)
            logger.info(f"✅ Saved query for {kpi_id}")
        except Exception as e:
            logger.error(f"❌ Error saving query: {e}")
    
    def get_all_kpis(self) -> list:
        """Get all KPI definitions."""
        kpis = []
        for category in self._kpi_data.get('categories', []):
            for kpi in category.get('kpis', []):
                kpis.append({
                    'id': kpi['id'],
                    'name': kpi['name'],
                    'category': category['id'],
                    'has_query': kpi['id'] in self._queries
                })
        return kpis
    
    def get_kpi_metadata(self, kpi_id: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a specific KPI."""
        for category in self._kpi_data.get('categories', []):
            for kpi in category.get('kpis', []):
                if kpi['id'] == kpi_id:
                    return {
                        'id': kpi['id'],
                        'name': kpi['name'],
                        'description': kpi.get('description', ''),
                        'category': category['id'],
                        'unit': kpi.get('unit', ''),
                        'formula': kpi.get('formula', ''),
                        'has_query': kpi_id in self._queries
                    }
        return None