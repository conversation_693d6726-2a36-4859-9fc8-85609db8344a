"""
KPI Query Generator - Dynamic query generation using existing pipeline.
Generates SQL queries for KPIs on-demand without hardcoding.
"""

import logging
from typing import Dict, Any, Optional
import asyncio
from datetime import datetime

logger = logging.getLogger(__name__)


class KpiQueryGenerator:
    """
    Generates KPI queries dynamically using the existing multi-agent pipeline.
    Treats each KPI as a natural language question.
    """
    
    def __init__(self, sector: str = "cambio", client_id: str = "L2M"):
        self.sector = sector
        self.client_id = client_id
        self._generated_queries = {}
        
    async def generate_query_for_kpi(self, kpi_data: Dict[str, Any]) -> Optional[str]:
        """
        Generate SQL query for a KPI using the existing pipeline.
        
        Args:
            kpi_data: KPI metadata including id, name, description, formula
            
        Returns:
            Generated SQL query or None if generation fails
        """
        kpi_id = kpi_data.get('id')
        logger.info(f"🔧 Generating query for KPI {kpi_id}")
        
        try:
            # Formulate natural language question based on KPI
            question = self._formulate_kpi_question(kpi_data)
            logger.info(f"📝 Question: {question}")
            
            # Use the existing optimized workflow
            from src.graphs.optimized_graph import create_optimized_workflow
            
            # Create workflow
            app = create_optimized_workflow(sector=self.sector, client_id=self.client_id)
            
            # Build state for KPI generation
            initial_state = {
                "messages": [{"role": "user", "content": question}],
                "question": question,
                "client_id": self.client_id,
                "sector": self.sector,
                "thread_id": f"kpi_gen_{kpi_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                "context": {
                    "mode": "kpi_generation",
                    "kpi_id": kpi_id,
                    "kpi_metadata": kpi_data,
                    "require_single_value": True,
                    "skip_business_analysis": True  # Skip analysis for faster generation
                }
            }
            
            # Invoke workflow
            result = await app.ainvoke(initial_state)
            
            if result and result.get('sql_query'):
                query = result['sql_query']
                confidence = result.get('confidence_score', 0)
                
                logger.info(f"✅ Query generated for {kpi_id} (confidence: {confidence:.2%})")
                
                # Validate query has necessary components
                if self._validate_kpi_query(query, kpi_data):
                    self._generated_queries[kpi_id] = {
                        'query': query,
                        'confidence': confidence,
                        'generated_at': datetime.now().isoformat()
                    }
                    return query
                else:
                    logger.error(f"❌ Generated query failed validation for {kpi_id}")
                    return None
            else:
                logger.error(f"❌ No query generated for {kpi_id}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error generating query for {kpi_id}: {e}")
            return None
    
    def _formulate_kpi_question(self, kpi_data: Dict[str, Any]) -> str:
        """
        Formulate a natural language question for the KPI.
        Uses the KPI metadata to create a precise question.
        """
        kpi_id = kpi_data.get('id', '')
        name = kpi_data.get('name', '')
        description = kpi_data.get('description', '')
        formula = kpi_data.get('formula', '')
        unit = kpi_data.get('unit', '')
        
        # Build question based on KPI characteristics
        base_questions = {
            # Volume metrics
            'total_volume': "Qual o volume total em valor (soma de valor_me) de todas as operações de câmbio dos últimos 12 meses para o cliente :client_id? Retorne apenas o valor total.",
            'volume_by_currency': "Qual o volume total em valor agrupado por moeda (id_moeda) dos últimos 12 meses para o cliente :client_id?",
            'growth_percentage': "Qual o crescimento percentual do volume comparando o mês atual com o mês anterior para o cliente :client_id?",
            
            # Ticket metrics
            'average_ticket': "Qual o valor médio (ticket médio) das operações de câmbio (média de valor_me) dos últimos 12 meses para o cliente :client_id? Retorne apenas o valor médio.",
            
            # Spread metrics
            'average_spread': "Qual o spread médio percentual das operações de câmbio dos últimos 6 meses para o cliente :client_id? Calcule como a diferença percentual entre taxa_cambio e taxa_base.",
            
            # Conversion metrics
            'conversion_rate': "Qual a taxa de conversão (percentual de operações com status 4 ou 7 sobre o total) dos últimos 12 meses para o cliente :client_id?",
            
            # Retention metrics
            'retention_rate': "Qual a taxa de retenção de clientes comparando os últimos 6 meses com os 6 meses anteriores? Calcule quantos clientes continuaram operando.",
            
            # Operational metrics
            'operations_per_analyst': "Qual a média de operações por analista (id_funcionario_criador) no último mês? Divida o total de operações pelo número de analistas únicos.",
            'average_settlement_time': "Qual o tempo médio de liquidação das operações em dias? Calcule a diferença entre data_liquidacao e data_operacao.",
            'approval_rate': "Qual a taxa de aprovação das operações (percentual com status aprovado) nos últimos 12 meses para o cliente :client_id?",
            
            # Risk metrics
            'var_daily': "Qual o Value at Risk (VaR) diário das operações de câmbio? Use percentil 95 das variações de valor.",
            'concentration_risk': "Qual a concentração de risco por moeda? Calcule o percentual do volume total por moeda.",
            
            # Quality metrics
            'error_rate': "Qual a taxa de erro nas operações (percentual com status de erro) nos últimos 12 meses?",
            'stp_rate': "Qual a taxa de STP (Straight Through Processing) das operações nos últimos 12 meses?"
        }
        
        # Use predefined question if available
        if kpi_id in base_questions:
            return base_questions[kpi_id]
        
        # Otherwise, build question from metadata
        question_parts = []
        
        # Start with the calculation type
        if 'média' in name.lower() or 'average' in formula.lower():
            question_parts.append(f"Qual a média de {description.lower()}")
        elif 'total' in name.lower() or 'sum' in formula.lower():
            question_parts.append(f"Qual o total de {description.lower()}")
        elif 'taxa' in name.lower() or 'rate' in formula.lower():
            question_parts.append(f"Qual a taxa de {description.lower()}")
        elif 'percentual' in name.lower() or '%' in unit:
            question_parts.append(f"Qual o percentual de {description.lower()}")
        else:
            question_parts.append(f"Calcule {name}")
        
        # Add time period
        question_parts.append("dos últimos 12 meses")
        
        # Add client filter
        question_parts.append("para o cliente :client_id")
        
        # Add return instruction
        question_parts.append("? Retorne apenas o valor calculado.")
        
        return " ".join(question_parts)
    
    def _validate_kpi_query(self, query: str, kpi_data: Dict[str, Any]) -> bool:
        """
        Validate that the generated query is appropriate for the KPI.
        """
        query_lower = query.lower()
        
        # Basic validations
        if not query_lower.strip().startswith('select'):
            logger.error("Query must start with SELECT")
            return False
        
        # Check for client_id parameter
        if ':client_id' not in query:
            logger.error("Query must include :client_id parameter")
            return False
        
        # Check for dangerous operations
        dangerous = ['drop', 'delete', 'truncate', 'update', 'insert', 'create', 'alter']
        for word in dangerous:
            if word in query_lower:
                logger.error(f"Query cannot contain {word.upper()}")
                return False
        
        # KPI-specific validations
        kpi_id = kpi_data.get('id', '')
        
        # Volume KPIs should use SUM
        if 'volume' in kpi_id and 'sum(' not in query_lower:
            logger.warning(f"Volume KPI {kpi_id} should use SUM aggregation")
        
        # Average KPIs should use AVG
        if 'average' in kpi_id and 'avg(' not in query_lower:
            logger.warning(f"Average KPI {kpi_id} should use AVG aggregation")
        
        # Rate KPIs should calculate percentage
        if 'rate' in kpi_id and ('count(' not in query_lower or '100' not in query):
            logger.warning(f"Rate KPI {kpi_id} should calculate percentage")
        
        return True
    
    def get_generated_query(self, kpi_id: str) -> Optional[Dict[str, Any]]:
        """Get a previously generated query."""
        return self._generated_queries.get(kpi_id)
    
    def get_all_generated_queries(self) -> Dict[str, Dict[str, Any]]:
        """Get all generated queries."""
        return self._generated_queries.copy()