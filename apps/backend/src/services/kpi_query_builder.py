"""
KPI Query Builder - Template-based SQL generation.
Generates reliable SQL queries for KPIs based on their metadata without using LLM.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class KpiQueryBuilder:
    """
    Builds SQL queries for KPIs using templates and metadata.
    This provides fast, reliable query generation without LLM calls.
    """
    
    def __init__(self, sector: str = "cambio"):
        self.sector = sector
        self.table_name = "boleta"  # Main table for exchange operations
        
    def build_query(self, kpi_data: Dict[str, Any]) -> Optional[str]:
        """
        Build SQL query for a KPI based on its metadata.
        
        Args:
            kpi_data: KPI metadata including id, name, formula, unit
            
        Returns:
            SQL query string or None if cannot build
        """
        kpi_id = kpi_data.get('id', '')
        logger.info(f"🔨 Building query for KPI {kpi_id}")
        
        # Get the appropriate builder method
        builder_method = getattr(self, f'_build_{kpi_id}', None)
        
        if builder_method:
            # Use specific builder
            query = builder_method(kpi_data)
        else:
            # Use generic builder based on KPI characteristics
            query = self._build_generic(kpi_data)
        
        if query:
            logger.info(f"✅ Query built for {kpi_id}")
            return query
        else:
            logger.warning(f"⚠️ Could not build query for {kpi_id}")
            return None
    
    # Specific builders for critical KPIs
    
    def _build_total_volume(self, kpi_data: Dict[str, Any]) -> str:
        """Build query for total volume KPI."""
        return f"""
            SELECT COALESCE(SUM(valor_me), 0) as total_volume
            FROM {self.table_name}
            WHERE data_operacao >= CURRENT_DATE - INTERVAL '12 months'
            AND id_cliente = :client_id
            AND valor_me IS NOT NULL
        """
    
    def _build_volume_by_currency(self, kpi_data: Dict[str, Any]) -> str:
        """Build query for volume by currency KPI."""
        return f"""
            SELECT 
                id_moeda as currency,
                COALESCE(SUM(valor_me), 0) as volume
            FROM {self.table_name}
            WHERE data_operacao >= CURRENT_DATE - INTERVAL '12 months'
            AND id_cliente = :client_id
            AND valor_me IS NOT NULL
            GROUP BY id_moeda
            ORDER BY volume DESC
        """
    
    def _build_average_ticket(self, kpi_data: Dict[str, Any]) -> str:
        """Build query for average ticket KPI."""
        return f"""
            SELECT AVG(valor_me) as average_ticket
            FROM {self.table_name}
            WHERE data_operacao >= CURRENT_DATE - INTERVAL '12 months'
            AND id_cliente = :client_id
            AND valor_me IS NOT NULL
            AND valor_me > 0
        """
    
    def _build_average_spread(self, kpi_data: Dict[str, Any]) -> str:
        """Build query for average spread KPI."""
        return f"""
            SELECT AVG(
                CASE
                    WHEN taxa_base > 0 THEN
                        ABS((taxa_cambio - taxa_base) / taxa_base) * 100
                    ELSE 0
                END
            ) as average_spread
            FROM {self.table_name}
            WHERE data_operacao >= CURRENT_DATE - INTERVAL '6 months'
            AND id_cliente = :client_id
            AND taxa_cambio IS NOT NULL
            AND taxa_base IS NOT NULL
            AND taxa_base > 0
        """
    
    def _build_conversion_rate(self, kpi_data: Dict[str, Any]) -> str:
        """Build query for conversion rate KPI."""
        return f"""
            SELECT
                CASE
                    WHEN COUNT(*) > 0 THEN
                        ROUND(
                            COUNT(CASE WHEN id_boleta_status IN (4, 7) THEN 1 END) * 100.0 / COUNT(*),
                            2
                        )
                    ELSE 0
                END as conversion_rate
            FROM {self.table_name}
            WHERE data_operacao >= CURRENT_DATE - INTERVAL '12 months'
            AND id_cliente = :client_id
        """
    
    def _build_retention_rate(self, kpi_data: Dict[str, Any]) -> str:
        """Build query for retention rate KPI."""
        return f"""
            WITH current_period AS (
                SELECT DISTINCT id_cliente
                FROM {self.table_name}
                WHERE data_operacao >= CURRENT_DATE - INTERVAL '6 months'
                AND data_operacao < CURRENT_DATE
            ),
            previous_period AS (
                SELECT DISTINCT id_cliente
                FROM {self.table_name}
                WHERE data_operacao >= CURRENT_DATE - INTERVAL '12 months'
                AND data_operacao < CURRENT_DATE - INTERVAL '6 months'
            ),
            retained_clients AS (
                SELECT COUNT(*) as retained
                FROM current_period c
                INNER JOIN previous_period p ON c.id_cliente = p.id_cliente
            )
            SELECT
                CASE
                    WHEN (SELECT COUNT(*) FROM previous_period) > 0 THEN
                        ROUND(
                            (SELECT retained FROM retained_clients) * 100.0 / 
                            (SELECT COUNT(*) FROM previous_period),
                            2
                        )
                    ELSE 0
                END as retention_rate
        """
    
    def _build_operations_per_analyst(self, kpi_data: Dict[str, Any]) -> str:
        """Build query for operations per analyst KPI."""
        return f"""
            SELECT 
                ROUND(
                    COUNT(*) * 1.0 / 
                    GREATEST(
                        COUNT(DISTINCT id_funcionario_criador),
                        1
                    ),
                    2
                ) as operations_per_analyst
            FROM {self.table_name}
            WHERE data_criacao >= CURRENT_DATE - INTERVAL '30 days'
            AND id_funcionario_criador IS NOT NULL
        """
    
    def _build_average_settlement_time(self, kpi_data: Dict[str, Any]) -> str:
        """Build query for average settlement time KPI."""
        return f"""
            SELECT 
                AVG(
                    EXTRACT(EPOCH FROM (data_liquidacao - data_operacao)) / 86400
                ) as avg_settlement_days
            FROM {self.table_name}
            WHERE data_operacao >= CURRENT_DATE - INTERVAL '12 months'
            AND id_cliente = :client_id
            AND data_liquidacao IS NOT NULL
            AND data_operacao IS NOT NULL
        """
    
    def _build_approval_rate(self, kpi_data: Dict[str, Any]) -> str:
        """Build query for approval rate KPI."""
        return f"""
            SELECT
                CASE
                    WHEN COUNT(*) > 0 THEN
                        ROUND(
                            COUNT(CASE WHEN id_boleta_status = 4 THEN 1 END) * 100.0 / COUNT(*),
                            2
                        )
                    ELSE 0
                END as approval_rate
            FROM {self.table_name}
            WHERE data_operacao >= CURRENT_DATE - INTERVAL '12 months'
            AND id_cliente = :client_id
        """
    
    def _build_growth_percentage(self, kpi_data: Dict[str, Any]) -> str:
        """Build query for growth percentage KPI."""
        return f"""
            WITH current_month AS (
                SELECT COALESCE(SUM(valor_me), 0) as volume
                FROM {self.table_name}
                WHERE data_operacao >= DATE_TRUNC('month', CURRENT_DATE)
                AND data_operacao < DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month'
                AND id_cliente = :client_id
            ),
            previous_month AS (
                SELECT COALESCE(SUM(valor_me), 0) as volume
                FROM {self.table_name}
                WHERE data_operacao >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 month'
                AND data_operacao < DATE_TRUNC('month', CURRENT_DATE)
                AND id_cliente = :client_id
            )
            SELECT
                CASE
                    WHEN (SELECT volume FROM previous_month) > 0 THEN
                        ROUND(
                            ((SELECT volume FROM current_month) - (SELECT volume FROM previous_month)) * 100.0 / 
                            (SELECT volume FROM previous_month),
                            2
                        )
                    ELSE 0
                END as growth_percentage
        """
    
    def _build_generic(self, kpi_data: Dict[str, Any]) -> Optional[str]:
        """
        Build generic query based on KPI metadata.
        This handles KPIs without specific builders.
        """
        kpi_id = kpi_data.get('id', '')
        name = kpi_data.get('name', '').lower()
        formula = kpi_data.get('formula', '').lower()
        unit = kpi_data.get('unit', '').lower()
        
        # Determine aggregation type
        if 'média' in name or 'average' in formula or 'avg' in formula:
            aggregation = "AVG"
            default_value = "NULL"
        elif 'total' in name or 'soma' in name or 'sum' in formula:
            aggregation = "SUM"
            default_value = "0"
        elif 'contagem' in name or 'count' in formula:
            aggregation = "COUNT"
            default_value = "0"
        elif 'máximo' in name or 'max' in formula:
            aggregation = "MAX"
            default_value = "NULL"
        elif 'mínimo' in name or 'min' in formula:
            aggregation = "MIN"
            default_value = "NULL"
        else:
            # Default to count
            aggregation = "COUNT"
            default_value = "0"
        
        # Determine value column
        if 'volume' in name or 'valor' in name:
            value_column = "valor_me"
        elif 'taxa' in name or 'rate' in name:
            value_column = "taxa_cambio"
        elif 'spread' in name:
            value_column = "ABS(taxa_cambio - taxa_base)"
        else:
            value_column = "*"  # For COUNT
        
        # Determine time period
        if 'diário' in name or 'daily' in name:
            time_period = "1 day"
        elif 'semanal' in name or 'weekly' in name:
            time_period = "7 days"
        elif 'mensal' in name or 'monthly' in name:
            time_period = "1 month"
        elif 'trimestral' in name or 'quarterly' in name:
            time_period = "3 months"
        elif 'anual' in name or 'yearly' in name:
            time_period = "12 months"
        else:
            time_period = "12 months"  # Default
        
        # Build query
        if aggregation == "COUNT" and value_column == "*":
            query = f"""
                SELECT COALESCE({aggregation}({value_column}), {default_value}) as {kpi_id}
                FROM {self.table_name}
                WHERE data_operacao >= CURRENT_DATE - INTERVAL '{time_period}'
                AND id_cliente = :client_id
            """
        else:
            query = f"""
                SELECT COALESCE({aggregation}({value_column}), {default_value}) as {kpi_id}
                FROM {self.table_name}
                WHERE data_operacao >= CURRENT_DATE - INTERVAL '{time_period}'
                AND id_cliente = :client_id
                AND {value_column} IS NOT NULL
            """
        
        return query