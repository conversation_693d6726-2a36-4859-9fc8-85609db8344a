{"metadata": {"version": "2.0", "sector": "exchange", "lastUpdated": "2025-07-05", "database_structure": {"corrected": true, "main_table": "boleta", "previous_table": "cambio", "client_mapping": {"L2M": 334}, "column_mappings": {"valor_moeda_estrangeira": "valor_me", "taxa_aplicada": "taxa_cambio", "taxa_referencia": "taxa_base", "codigo_moeda": "JOIN com boleta_moeda.simbolo", "cliente_id": "id_cliente"}}, "implementation_status": "queries_corrected_with_real_data"}, "categories": [{"id": "operational", "name": "KPIs Operacionais", "kpis": [{"id": "total_volume", "name": "Volume Total Negociado", "description": "Mede o tamanho da operação e é o indicador primário de crescimento", "formula": "SUM(valor_me) FROM boleta WHERE data_operacao [periodo] AND id_cliente = [client_numeric_id]", "unit": "Valor monetário (R$, US$, etc.)", "frequency": "Diária/Semanal/Mensal", "importance": "Indicador primário de crescimento", "implementation": "corrected_query_in_kpi_calculator", "test_results": {"client_814": {"2025_operations": 40, "total_volume": "638406.75", "avg_ticket": "15960.17"}}, "sql_query": null, "query_metadata": {"generated_at": null, "generated_by": null, "confidence": null, "validation_passed": null, "version": 0, "last_modified": null, "modification_history": []}}, {"id": "volume_by_currency", "name": "Volume por Moeda", "description": "Análise de concentração e exposição por moeda", "formula": "SELECT bm.simbolo, SUM(b.valor_me) FROM boleta b JOIN boleta_moeda bm ON b.id_moeda = bm.id GROUP BY bm.simbolo", "unit": "Valor monetário (R$, US$, etc.)", "frequency": "Diária/Semanal/Mensal", "importance": "Análise de concentração e exposição por moeda", "implementation": "corrected_query_with_join_boleta_moeda"}, {"id": "growth_percentage", "name": "Crescimento Percentual", "description": "Análise de tendências e sazonalidade", "formula": "(Volume do período atual - Volume do período anterior) / Volume do período anterior × 100", "unit": "Percentual (%)", "frequency": "Mensal/Trimestral/Anual", "importance": "Análise de tendências e sazonalidade"}, {"id": "average_ticket", "name": "Ticket <PERSON>", "description": "Indica perfil de clientes e operações", "formula": "AVG(valor_me) FROM boleta WHERE data_operacao [periodo] AND id_cliente = [client_numeric_id]", "unit": "Valor monetário (R$, US$, etc.)", "frequency": "Semanal/Mensal", "importance": "Indica perfil de clientes e operações", "implementation": "corrected_query_in_kpi_calculator", "sql_query": null, "query_metadata": {"generated_at": null, "generated_by": null, "confidence": null, "validation_passed": null, "version": 0, "last_modified": null, "modification_history": []}}, {"id": "average_settlement_time", "name": "Tempo Médio para Liquidação", "description": "Eficiência operacional", "formula": "Média do tempo decorrido entre a criação da operação e sua liquidação final", "unit": "Horas/Dias", "frequency": "Semanal/Mensal", "importance": "Eficiência operacional"}, {"id": "operations_per_analyst", "name": "Operações por Analista/Dia", "description": "Produtividade", "formula": "Número total de operações processadas / (Número de analistas × Dias úteis no período)", "unit": "Operações/Analista/Dia", "frequency": "Diária/Semanal", "importance": "Produtividade", "sql_query": null, "query_metadata": {"generated_at": null, "generated_by": null, "confidence": null, "validation_passed": null, "version": 0, "last_modified": null, "modification_history": []}}, {"id": "automation_rate", "name": "Taxa de Automação", "description": "Nível de digitalização dos processos", "formula": "(Número de operações processadas sem intervenção manual / Total de operações) × 100", "unit": "Percentual (%)", "frequency": "Mensal/Trimestral", "importance": "Nível de digitalização dos processos"}]}, {"id": "financial", "name": "KPIs Financeiros", "kpis": [{"id": "average_spread", "name": "Spread Médio", "description": "Principal indicador de rentabilidade", "formula": "AVG(CASE WHEN tipo_operacao='VENDA' THEN ((taxa_cambio-taxa_base)/taxa_base)*100 WHEN tipo_operacao='COMPRA' THEN ((taxa_base-taxa_cambio)/taxa_base)*100 ELSE 0 END) FROM boleta", "unit": "Percentual (%)", "frequency": "Diária/Semanal/Mensal", "importance": "Principal indicador de rentabilidade", "implementation": "corrected_query_using_taxa_base_as_reference", "sql_query": null, "query_metadata": {"generated_at": null, "generated_by": null, "confidence": null, "validation_passed": null, "version": 0, "last_modified": null, "modification_history": []}}, {"id": "gross_margin", "name": "<PERSON><PERSON><PERSON>", "description": "Mede o ganho total em spreads", "formula": "SUM(valor_me * CASE WHEN tipo_operacao='VENDA' THEN (taxa_cambio-taxa_base) WHEN tipo_operacao='COMPRA' THEN (taxa_base-taxa_cambio) ELSE 0 END) FROM boleta", "unit": "Valor monetário (R$, US$, etc.)", "frequency": "Semanal/Mensal", "importance": "Mede o ganho total em spreads", "implementation": "corrected_query_with_real_spread_calculation"}, {"id": "net_margin", "name": "<PERSON><PERSON><PERSON>", "description": "Rentabilidade real das operações", "formula": "Margem Bruta - SUM(tarifa_bancaria + iof_cambio_valor + irrf_valor + iss_valor + pis_valor + cide_valor + cofins_valor) FROM boleta", "unit": "Valor monetário (R$, US$, etc.)", "frequency": "Mensal/Trimestral", "importance": "Rentabilidade real das operações", "implementation": "corrected_query_with_all_operation_costs"}, {"id": "operations_roi", "name": "ROI de Operações", "description": "Retorno sobre investimento", "formula": "(Receita total - Custos operacionais) / Custos operacionais × 100", "unit": "Percentual (%)", "frequency": "Mensal/Trimestral", "importance": "Retorno sobre investimento"}, {"id": "cost_per_operation", "name": "Custo por Operação", "description": "Eficiência de custo", "formula": "Total de custos operacionais / Número de operações", "unit": "Valor monetário (R$, US$, etc.)", "frequency": "Mensal/Trimestral", "importance": "Eficiência de custo"}, {"id": "cost_to_income_ratio", "name": "Cost-to-Income Ratio", "description": "Eficiência operacional", "formula": "(Total de custos operacionais / Receita total) × 100", "unit": "Percentual (%)", "frequency": "Mensal/Trimestral", "importance": "Eficiência operacional"}]}, {"id": "client", "name": "KPIs de Clientes", "kpis": [{"id": "conversion_rate", "name": "Taxa de Conversão", "description": "Eficácia comercial", "formula": "(Número de leads convertidos em clientes / Total de leads) × 100", "unit": "Percentual (%)", "frequency": "Mensal/Trimestral", "importance": "Eficácia comercial", "sql_query": null, "query_metadata": {"generated_at": null, "generated_by": null, "confidence": null, "validation_passed": null, "version": 0, "last_modified": null, "modification_history": []}}, {"id": "retention_rate", "name": "Taxa de Retenção", "description": "Fidelização de clientes", "sql_query": null, "query_metadata": {"generated_at": null, "generated_by": null, "confidence": null, "validation_passed": null, "version": 0, "last_modified": null, "modification_history": []}, "formula": "(Número de clientes ativos no fim do período que também estavam ativos no início / Total de clientes ativos no início do período) × 100", "unit": "Percentual (%)", "frequency": "Mensal/Trimestral/Anual", "importance": "Fidelização de clientes"}, {"id": "churn_rate", "name": "Churn Rate", "description": "Perda de clientes", "formula": "(Número de clientes que deixaram de operar / Total de clientes no início do período) × 100", "unit": "Percentual (%)", "frequency": "Mensal/Trimestral", "importance": "Perda de clientes"}, {"id": "ltv", "name": "Cliente Lifetime Value (LTV)", "description": "Valor de longo prazo", "formula": "Valor médio por cliente por período × Número médio de períodos de relacionamento", "unit": "Valor monetário (R$, US$, etc.)", "frequency": "Trimestral/Anual", "importance": "Valor de longo prazo"}, {"id": "client_concentration", "name": "Concentração de Clientes", "description": "Risco de dependência", "formula": "(Volume dos top X clientes / Volume total) × 100", "unit": "Percentual (%)", "frequency": "Mensal/Trimestral", "importance": "Risco de dependência"}]}, {"id": "market", "name": "KPIs de Mercado e Moedas", "kpis": [{"id": "currency_exposure", "name": "Exposição por Moeda", "description": "Gestão de risco cambial", "formula": "Soma das posições abertas em determinada moeda", "unit": "Valor monetário (R$, US$, etc.)", "frequency": "<PERSON><PERSON><PERSON>", "importance": "Gestão de risco cambial"}, {"id": "rate_volatility", "name": "Volatilidade das Taxas", "description": "Anális<PERSON> de risco", "formula": "<PERSON><PERSON> das taxas por moeda em um período", "unit": "Pontos percentuais", "frequency": "Diária/Semanal", "importance": "Anális<PERSON> de risco"}, {"id": "limit_utilization", "name": "Percentual de Limite Utilizado", "description": "Controle de limites", "formula": "(Exposição média / Limite estabelecido) × 100", "unit": "Percentual (%)", "frequency": "<PERSON><PERSON><PERSON>", "importance": "Controle de limites"}, {"id": "var", "name": "VaR (Value at Risk)", "description": "Quantificação de risco", "formula": "Cálculo estatístico baseado em histórico de taxas e exposição atual", "unit": "Valor monetário (R$, US$, etc.)", "frequency": "<PERSON><PERSON><PERSON>", "importance": "Quantificação de risco"}]}, {"id": "compliance", "name": "KPIs de Compliance e Risco", "kpis": [{"id": "reported_operations_rate", "name": "Taxa de Operações Reportadas", "description": "Compliance regulatório", "formula": "(Número de operações reportadas a órgãos reguladores / Total de operações) × 100", "unit": "Percentual (%)", "frequency": "Mensal/Trimestral", "importance": "Compliance regulatório"}, {"id": "compliance_analysis_time", "name": "Tempo de Análise de Compliance", "description": "Eficiência regulatória", "formula": "Tempo médio entre submissão de operação e aprovação de compliance", "unit": "Horas/Dias", "frequency": "Semanal/Mensal", "importance": "Eficiência regulatória"}, {"id": "compliance_rejection_rate", "name": "Taxa de Rejeição de Compliance", "description": "Rigor dos controles", "formula": "(Número de operações rejeitadas por compliance / Total de operações analisadas) × 100", "unit": "Percentual (%)", "frequency": "Mensal/Trimestral", "importance": "Rigor dos controles"}, {"id": "fraud_rate", "name": "Taxa de Fraude", "description": "Controle de fraudes", "formula": "(Valor de operações fraudulentas / Valor total transacionado) × 100", "unit": "Percentual (%)", "frequency": "Mensal/Trimestral", "importance": "Controle de fraudes"}]}, {"id": "team", "name": "KPIs de Equipe", "kpis": [{"id": "volume_per_operator", "name": "Volume por Operador", "description": "Performance individual", "formula": "Soma do valor das operações por operador/consultor", "unit": "Valor monetário (R$, US$, etc.)", "frequency": "Semanal/Mensal", "importance": "Performance individual"}, {"id": "average_spread_by_operator", "name": "Spread Médio por Operador", "description": "Habilidade de negociação", "formula": "Média do spread obtido nas operações por operador", "unit": "Percentual (%)", "frequency": "Semanal/Mensal", "importance": "Habilidade de negociação"}, {"id": "sla_rate_by_team", "name": "Taxa de SLA por Equipe", "description": "Qualidade de serviço", "formula": "(Número de operações concluídas dentro do SLA / Total de operações) por equipe × 100", "unit": "Percentual (%)", "frequency": "Semanal/Mensal", "importance": "Qualidade de serviço"}, {"id": "talent_retention_rate", "name": "Taxa de Retenção de Talentos", "description": "Gestão de pessoas", "formula": "(Número de colaboradores que permanecem na empresa no período / Total de colaboradores no início do período) × 100", "unit": "Percentual (%)", "frequency": "Trimestral/Anual", "importance": "Gestão de pessoas"}]}, {"id": "strategic", "name": "KPIs Estratégicos Consolidados", "kpis": [{"id": "yoy_growth_rate", "name": "Taxa de Crescimento YoY", "description": "Crescimento de longo prazo", "formula": "(Volume anual atual - Volume anual anterior) / Volume anual anterior × 100", "unit": "Percentual (%)", "frequency": "<PERSON><PERSON>", "importance": "Crescimento de longo prazo"}, {"id": "diversification_index", "name": "Índice de Diversificação", "description": "Concentração de negócio", "formula": "1 - (Soma dos quadrados dos percentuais de participação de cada produto/moeda)", "unit": "Índice (0-1)", "frequency": "Trimestral/Anual", "importance": "Concentração de negócio"}, {"id": "ebitda_volume_ratio", "name": "EBITDA por Volume", "description": "Rentabilidade ajustada", "formula": "EBITDA / Volume total transacionado", "unit": "Percentual (%)", "frequency": "Trimestral/Anual", "importance": "Rentabilidade ajustada"}, {"id": "ltv_cac_ratio", "name": "LTV/CAC Ratio", "description": "Eficiência de aquisição", "formula": "Lifetime Value do Cliente / Custo de Aquisição do Cliente", "unit": "Razão", "frequency": "Trimestral/Anual", "importance": "Eficiência de aquisição"}]}], "managerialQuestions": [{"id": "op1", "category": "operations", "question": "Qual foi o volume total de transações no último mês/trimestre/ano, e como isso se compara com períodos anteriores?", "kpiReferences": ["total_volume", "growth_percentage"], "importance": "Avaliação de crescimento e identificação de sazonalidade"}, {"id": "op2", "category": "operations", "question": "<PERSON><PERSON><PERSON> s<PERSON> as 10 maiores operações em andamento e qual seu status atual?", "kpiReferences": ["total_volume"], "importance": "Gestão de operações de alto valor que precisam de atenção especial"}, {"id": "op3", "category": "operations", "question": "Qual é o tempo médio para liquidação de uma operação, desde sua abertura até a finalização?", "kpiReferences": ["average_settlement_time"], "importance": "Identificação de gargalos operacionais e oportunidades de melhoria de processo"}, {"id": "cs1", "category": "clients", "question": "Quais são os 20 maiores clientes por volume transacionado nos últimos 12 meses?", "kpiReferences": ["total_volume", "client_concentration"], "importance": "Identificação de clientes estratégicos e gestão de relacionamento"}, {"id": "cs3", "category": "clients", "question": "Qual a taxa de retenção de clientes e o lifetime value médio por segmento?", "kpiReferences": ["retention_rate", "ltv"], "importance": "Avaliação de fidelização e valor estratégico de longo prazo"}, {"id": "mm1", "category": "market", "question": "Qual a distribuição de volume por moeda nos últimos 6 meses?", "kpiReferences": ["volume_by_currency"], "importance": "Gestão de tesouraria e planejamento de liquidez"}, {"id": "mm5", "category": "market", "question": "Qual a exposição atual da empresa por moeda?", "kpiReferences": ["currency_exposure", "limit_utilization"], "importance": "Gestão de risco cambial e limites de exposição"}, {"id": "fr1", "category": "financial", "question": "Qual o lucro líquido médio por tipo de operação nos últimos 3 meses?", "kpiReferences": ["net_margin"], "importance": "Análise de rentabilidade por produto/serviço"}, {"id": "cr1", "category": "compliance", "question": "Quais operações recentes possuem padrões similares às já classificadas como suspeitas?", "kpiReferences": ["reported_operations_rate"], "importance": "Prevenção a fraudes e lavagem de dinheiro"}, {"id": "ep1", "category": "team", "question": "Qual analista/operador possui o maior volume de transações no mês?", "kpiReferences": ["volume_per_operator"], "importance": "Avaliação de desempenho individual e reconhecimento"}], "alerts": [{"id": "ao1", "category": "operational", "name": "<PERSON><PERSON><PERSON> de Liquidez", "condition": "Volume de determinada moeda em caixa abaixo de X% do normal", "monitoring": "Comparar posição atual de cada moeda contra média histórica ou limite mínimo estabe<PERSON>o", "importance": "Evitar falta de moeda para liquidar operações"}, {"id": "am1", "category": "market", "name": "Alerta de Volatilidade", "condition": "Variações cambiais acima de X% em período curto", "monitoring": "Monitorar desvio padrão das variações em janelas móveis de tempo", "importance": "Gestão de exposição e oportunidades de negociação"}, {"id": "ac1", "category": "clients", "name": "Alerta de Inatividade", "condition": "Clientes importantes sem operações há X dias", "monitoring": "Calcular dias desde última operação por cliente e comparar com padrão histórico", "importance": "Prevenção de churn e ações proativas de retenção"}, {"id": "acr1", "category": "compliance", "name": "Alerta de Transações Suspeitas", "condition": "Operações atendendo a critérios de suspeição", "monitoring": "Aplicar algoritmos de detecção em tempo real a cada nova operação", "importance": "Prevenção a lavagem de dinheiro e conformidade regulatória"}, {"id": "af1", "category": "financial", "name": "Alerta de Rentabilidade", "condition": "Queda na rentabilidade média por operação", "monitoring": "Monitorar tendências de spread e margens realizadas", "importance": "Correção de estratégia de pricing e negociação"}], "implementation_notes": {"query_corrections_completed": "2025-07-05", "database_discovery": {"original_assumptions": {"table": "cambio", "client_field": "cliente_id (string)", "volume_field": "valor_moeda_estrangeira", "rate_fields": "taxa_aplicada, taxa_referencia"}, "actual_database_structure": {"table": "boleta", "client_field": "id_cliente (integer)", "volume_field": "valor_me", "rate_fields": "taxa_cambio, taxa_base", "currency_join": "JOIN boleta_moeda ON id_moeda", "status_join": "id_boleta_status references boleta_status"}, "client_mapping": {"L2M": 334, "most_active_clients": [814, 134, 538, 258]}, "data_validation": {"total_records": 23684, "date_range": "2017-06-23 to 2025-02-14", "test_client_814": {"2025_operations": 40, "total_volume_usd": 638406.75, "approval_rate": "100%", "avg_cost_per_operation": 74.14}}}, "kpi_categories_implemented": {"volume_metrics": 4, "performance_metrics": 6, "operational_metrics": 8, "risk_metrics": 5, "total_kpis_corrected": 34}, "implementation_location": "apps/backend/src/services/kpi_calculator.py", "testing_status": "all_queries_validated_with_real_data"}}