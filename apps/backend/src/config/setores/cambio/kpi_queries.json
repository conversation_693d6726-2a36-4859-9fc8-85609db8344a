{"total_volume": "\n            SELECT COALESCE(SUM(valor_me), 0) as total_volume\n            FROM boleta\n            WHERE data_operacao >= CURRENT_DATE - INTERVAL '12 months'\n            AND id_cliente = :client_id\n            AND valor_me IS NOT NULL\n        ", "metadata": {"total_volume": {"generated_at": "2025-07-08T23:27:28.180919", "generated_by": "query_builder"}, "average_ticket": {"generated_at": "2025-07-08T23:27:31.626443", "generated_by": "query_builder"}, "average_spread": {"generated_at": "2025-07-08T23:27:34.308212", "generated_by": "query_builder"}}, "average_ticket": "\n            SELECT AVG(valor_me) as average_ticket\n            FROM boleta\n            WHERE data_operacao >= CURRENT_DATE - INTERVAL '12 months'\n            AND id_cliente = :client_id\n            AND valor_me IS NOT NULL\n            AND valor_me > 0\n        ", "average_spread": "\n            SELECT AVG(\n                CASE\n                    WHEN taxa_base > 0 THEN\n                        ABS((taxa_cambio - taxa_base) / taxa_base) * 100\n                    ELSE 0\n                END\n            ) as average_spread\n            FROM boleta\n            WHERE data_operacao >= CURRENT_DATE - INTERVAL '6 months'\n            AND id_cliente = :client_id\n            AND taxa_cambio IS NOT NULL\n            AND taxa_base IS NOT NULL\n            AND taxa_base > 0\n        "}