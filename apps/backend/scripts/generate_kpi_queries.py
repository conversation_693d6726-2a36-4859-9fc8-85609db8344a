#!/usr/bin/env python3
"""
Generate KPI queries dynamically using the pipeline.
This script runs the query generation for all KPIs that don't have queries yet.
"""

import asyncio
import logging
import json
from pathlib import Path
from datetime import datetime

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Critical KPIs to generate first
PRIORITY_KPIS = [
    "total_volume",
    "average_ticket", 
    "average_spread",
    "conversion_rate",
    "retention_rate",
    "operations_per_analyst"
]


async def generate_kpi_queries():
    """Generate queries for KPIs that don't have them yet."""
    from src.services.kpi_query_generator import KpiQueryGenerator
    from src.services.kpi_query_manager_json import KpiQueryManagerJSON
    
    logger.info("🚀 Starting KPI query generation...")
    
    # Initialize managers
    query_manager = KpiQueryManagerJSON(sector="cambio")
    generator = KpiQueryGenerator(sector="cambio", client_id="L2M")
    
    # Get all KPIs
    all_kpis = query_manager.get_all_kpis()
    kpis_without_queries = [kpi for kpi in all_kpis if not kpi.get('has_query')]
    
    logger.info(f"📊 Found {len(kpis_without_queries)} KPIs without queries")
    
    # Sort to prioritize critical KPIs
    kpis_to_generate = []
    
    # Add priority KPIs first
    for kpi_id in PRIORITY_KPIS:
        kpi = next((k for k in kpis_without_queries if k['id'] == kpi_id), None)
        if kpi:
            kpis_to_generate.append(kpi)
    
    # Add remaining KPIs
    for kpi in kpis_without_queries:
        if kpi['id'] not in PRIORITY_KPIS:
            kpis_to_generate.append(kpi)
    
    # Generate queries
    success_count = 0
    failed_count = 0
    
    for i, kpi in enumerate(kpis_to_generate):
        kpi_id = kpi['id']
        logger.info(f"\n📝 [{i+1}/{len(kpis_to_generate)}] Generating query for {kpi_id} ({kpi['name']})...")
        
        try:
            # Get full KPI metadata
            kpi_data = query_manager.get_kpi_metadata(kpi_id)
            
            if not kpi_data:
                logger.error(f"❌ No metadata found for {kpi_id}")
                failed_count += 1
                continue
            
            # Generate query
            query = await generator.generate_query_for_kpi(kpi_data)
            
            if query:
                # Save query
                query_manager.save_query(kpi_id, query, {
                    'generated_by': 'generate_kpi_queries.py',
                    'generated_at': datetime.now().isoformat(),
                    'confidence': generator.get_generated_query(kpi_id).get('confidence', 0)
                })
                
                logger.info(f"✅ Successfully generated query for {kpi_id}")
                logger.info(f"   Query preview: {query[:100]}...")
                success_count += 1
            else:
                logger.error(f"❌ Failed to generate query for {kpi_id}")
                failed_count += 1
                
        except Exception as e:
            logger.error(f"❌ Error generating query for {kpi_id}: {e}")
            failed_count += 1
        
        # Add delay to avoid overwhelming the LLM
        if i < len(kpis_to_generate) - 1:
            logger.info("⏳ Waiting 2 seconds before next generation...")
            await asyncio.sleep(2)
    
    # Summary
    logger.info(f"\n📊 Generation complete!")
    logger.info(f"✅ Success: {success_count}")
    logger.info(f"❌ Failed: {failed_count}")
    logger.info(f"📁 Queries saved to: {query_manager.queries_path}")


async def test_generated_queries():
    """Test the generated queries by executing them."""
    from src.services.kpi_query_manager_json import KpiQueryManagerJSON
    from src.tools.db_utils import load_db_config, build_connection_string, get_engine
    from sqlalchemy import text
    
    logger.info("\n🧪 Testing generated queries...")
    
    query_manager = KpiQueryManagerJSON(sector="cambio")
    
    # Test priority KPIs
    for kpi_id in PRIORITY_KPIS:
        query = query_manager.get_kpi_query(kpi_id)
        
        if query:
            logger.info(f"\n📊 Testing {kpi_id}...")
            
            try:
                # Get database connection
                db_config = load_db_config(setor="cambio", cliente="L2M")
                connection_string = build_connection_string(db_config)
                engine = get_engine(connection_string)
                
                # Replace :client_id with actual value
                test_query = query.replace(':client_id', '334')
                
                with engine.connect() as conn:
                    result = conn.execute(text(test_query))
                    row = result.fetchone()
                    
                    if row and row[0] is not None:
                        value = float(row[0])
                        logger.info(f"✅ {kpi_id}: {value:,.2f}")
                    else:
                        logger.warning(f"⚠️ {kpi_id}: No data returned")
                        
            except Exception as e:
                logger.error(f"❌ Error testing {kpi_id}: {e}")
        else:
            logger.warning(f"⚠️ No query found for {kpi_id}")


async def main():
    """Main function."""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--test':
        # Test existing queries
        await test_generated_queries()
    else:
        # Generate queries
        await generate_kpi_queries()
        
        # Ask if user wants to test
        response = input("\nDo you want to test the generated queries? (y/n): ")
        if response.lower() == 'y':
            await test_generated_queries()


if __name__ == "__main__":
    asyncio.run(main())