#!/usr/bin/env python3
"""
Test KPI system locally with JSON-based queries.
Tests the KPI calculation service without pipeline integration.
"""

import asyncio
import logging
import json
from datetime import datetime
from pathlib import Path

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test configuration
TEST_KPIS = {
    "total_volume": "Volume Total Negociado",
    "average_ticket": "Ticket Médio",
    "average_spread": "Spread Médio"
}


def test_kpi_service_direct():
    """Test KPI service directly without full pipeline."""
    logger.info("🧪 Testing KPI Service directly...")
    
    try:
        from src.services.kpi_service import get_kpi_service
        
        # Get KPI service instance
        kpi_service = get_kpi_service()
        
        # Test loading KPI definitions
        logger.info("\n📋 Loading KPI definitions...")
        kpi_definitions = kpi_service.get_kpi_definitions(sector="cambio")
        
        logger.info(f"Found {len(kpi_definitions)} KPI definitions")
        
        # Show first few KPIs
        for kpi in kpi_definitions[:5]:
            logger.info(f"  - {kpi['id']}: {kpi['name']} ({kpi['category']})")
        
        # Test calculating specific KPIs
        logger.info("\n🔢 Testing KPI calculations...")
        
        for kpi_id, kpi_name in TEST_KPIS.items():
            logger.info(f"\nCalculating {kpi_name} ({kpi_id})...")
            
            # Find KPI definition
            kpi_def = next((k for k in kpi_definitions if k['id'] == kpi_id), None)
            
            if kpi_def:
                try:
                    # Calculate KPI value
                    result = kpi_service.calculate_kpi_value_from_dict(kpi_def, client_id="L2M")
                    
                    if result:
                        logger.info(f"✅ {kpi_name}:")
                        logger.info(f"   Value: {result['currentValue']:,.2f}")
                        logger.info(f"   Format: {result['format']}")
                        logger.info(f"   Category: {result['category']}")
                    else:
                        logger.warning(f"⚠️ No result for {kpi_name}")
                        
                except Exception as e:
                    logger.error(f"❌ Error calculating {kpi_name}: {e}")
            else:
                logger.warning(f"⚠️ KPI definition not found for {kpi_id}")
                
    except Exception as e:
        logger.error(f"❌ Error in KPI service test: {e}")
        import traceback
        traceback.print_exc()


def test_kpi_query_manager():
    """Test KPI Query Manager directly."""
    logger.info("\n🗄️ Testing KPI Query Manager (JSON)...")
    
    try:
        from src.services.kpi_query_manager_json import KpiQueryManagerJSON
        
        # Create query manager
        query_manager = KpiQueryManagerJSON(sector="cambio")
        
        # Test getting all KPIs
        all_kpis = query_manager.get_all_kpis()
        logger.info(f"Found {len(all_kpis)} active KPIs")
        
        # Check which KPIs have queries
        kpis_with_queries = [kpi for kpi in all_kpis if kpi.get('has_query')]
        logger.info(f"KPIs with existing queries: {len(kpis_with_queries)}")
        
        # Show KPIs without queries
        kpis_without_queries = [kpi for kpi in all_kpis if not kpi.get('has_query')]
        if kpis_without_queries:
            logger.info(f"KPIs without queries: {len(kpis_without_queries)}")
            for kpi in kpis_without_queries[:5]:
                logger.info(f"  - {kpi['id']}: {kpi['name']}")
        
        # Test getting specific KPI queries
        for kpi_id in TEST_KPIS.keys():
            logger.info(f"\nChecking query for {kpi_id}...")
            
            # Get query
            query = query_manager.get_kpi_query(kpi_id)
            
            if query:
                logger.info(f"✅ Found query for {kpi_id}")
                logger.info(f"   Query preview: {query[:100]}...")
            else:
                logger.warning(f"⚠️ No query found for {kpi_id}")
                
                # Check metadata
                metadata = query_manager.get_kpi_metadata(kpi_id)
                if metadata:
                    logger.info(f"   Metadata: {metadata.get('name', 'Unknown')}")
                    logger.info(f"   Has query: {bool(metadata.get('sql_query'))}")
                    
    except Exception as e:
        logger.error(f"❌ Error in query manager test: {e}")
        import traceback
        traceback.print_exc()


def test_database_connection():
    """Test direct database connection."""
    logger.info("\n🔌 Testing database connection...")
    
    try:
        from src.tools.db_utils import load_db_config, build_connection_string, get_engine
        from sqlalchemy import text
        
        # Load config for L2M
        db_config = load_db_config(setor="cambio", cliente="L2M")
        logger.info(f"✅ Loaded DB config for L2M")
        
        # Build connection
        connection_string = build_connection_string(db_config)
        engine = get_engine(connection_string)
        
        # Test simple query
        with engine.connect() as conn:
            result = conn.execute(text("SELECT COUNT(*) FROM boleta LIMIT 1"))
            count = result.scalar()
            logger.info(f"✅ Database connected! Total boletas: {count}")
            
            # Test KPI-specific queries
            logger.info("\n📊 Testing KPI queries directly...")
            
            # Total volume query
            result = conn.execute(text("""
                SELECT COALESCE(SUM(valor_me), 0) as total_volume
                FROM boleta
                WHERE data_operacao >= CURRENT_DATE - INTERVAL '1 month'
                AND id_cliente = 334
                AND valor_me IS NOT NULL
            """))
            volume = result.scalar()
            logger.info(f"✅ Total volume (1 month): {volume:,.2f}")
            
    except Exception as e:
        logger.error(f"❌ Database connection error: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Run all local tests."""
    logger.info("🚀 Starting Local KPI System Tests\n")
    
    # Test 1: Database connection
    test_database_connection()
    
    # Test 2: KPI Service
    test_kpi_service_direct()
    
    # Test 3: Query Manager
    test_kpi_query_manager()
    
    logger.info("\n✅ Local tests completed!")


if __name__ == "__main__":
    main()