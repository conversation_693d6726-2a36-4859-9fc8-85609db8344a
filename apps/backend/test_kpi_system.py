#!/usr/bin/env python3
"""
Test KPI system with real data.
Tests the complete flow: query generation, storage, and execution.
"""

import asyncio
import logging
import json
from datetime import datetime

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test configuration
TEST_KPIS = {
    "total_volume": "Volume Total Negociado",
    "average_ticket": "Ticket Médio",
    "average_spread": "Spread Médio"
}


async def test_kpi_calculation():
    """Test KPI calculation with dynamic queries."""
    from src.services.kpi_service import get_kpi_service
    
    logger.info("🧪 Testing KPI calculation...")
    
    kpi_service = get_kpi_service()
    
    # Test each KPI
    for kpi_id, kpi_name in TEST_KPIS.items():
        logger.info(f"\n📊 Testing {kpi_name} ({kpi_id})...")
        
        try:
            # This will trigger dynamic query generation if needed
            value = kpi_service._calculate_real_kpi_value(kpi_id, "L2M")
            
            if value is not None:
                logger.info(f"✅ {kpi_name}: {value:,.2f}")
            else:
                logger.warning(f"⚠️ {kpi_name}: No value returned")
                
        except Exception as e:
            logger.error(f"❌ Error calculating {kpi_name}: {e}")


async def test_query_generation():
    """Test direct query generation via graph."""
    from src.graphs.optimized_graph import create_optimized_workflow
    from langgraph.constants import END
    
    logger.info("\n🔧 Testing query generation via pipeline...")
    
    # Test generating a query for total volume
    question = "Qual o volume total em valor de todas as operações de câmbio dos últimos 12 meses? Retorne apenas o valor total somado."
    
    try:
        # Create the workflow
        app = create_optimized_workflow(sector="cambio", client_id="L2M")
        
        # Build initial state
        initial_state = {
            "messages": [{"role": "user", "content": question}],
            "question": question,
            "client_id": "L2M",
            "sector": "cambio",
            "thread_id": f"test_kpi_gen_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "context": {
                "mode": "kpi_generation",
                "kpi_id": "total_volume",
                "require_single_value": True
            }
        }
        
        # Invoke the workflow
        result = await app.ainvoke(initial_state)
        
        if result and result.get('sql_query'):
            logger.info(f"✅ Query generated successfully!")
            logger.info(f"SQL: {result['sql_query'][:200]}...")
            logger.info(f"Confidence: {result.get('confidence_score', 0):.2%}")
            
            # Test executing the query
            if result.get('results'):
                logger.info(f"Value: {result.get('results', 'N/A')}")
        else:
            logger.error("❌ No query generated")
            
    except Exception as e:
        logger.error(f"❌ Error generating query: {e}")


async def test_dashboard_endpoint():
    """Test dashboard API endpoints."""
    import httpx
    
    logger.info("\n🌐 Testing dashboard API...")
    
    async with httpx.AsyncClient() as client:
        try:
            # Test KPIs endpoint
            response = await client.get("http://localhost:8000/api/dashboard/kpis")
            
            if response.status_code == 200:
                kpis = response.json()
                logger.info(f"✅ Dashboard returned {len(kpis)} KPIs")
                
                # Show first few KPIs
                for kpi in kpis[:3]:
                    logger.info(f"  - {kpi.get('title', 'Unknown')}: {kpi.get('currentValue', 'N/A')}")
            else:
                logger.error(f"❌ Dashboard API error: {response.status_code}")
                
        except Exception as e:
            logger.warning(f"⚠️ Dashboard API not available: {e}")


def test_json_structure():
    """Test if KPI JSON has been updated with queries."""
    logger.info("\n📄 Testing KPI JSON structure...")
    
    json_path = "src/config/setores/cambio/kpis-exchange-json.json"
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            kpis_data = json.load(f)
        
        kpis_with_queries = 0
        total_kpis = 0
        
        for category in kpis_data.get('categories', []):
            for kpi in category.get('kpis', []):
                total_kpis += 1
                if kpi.get('sql_query'):
                    kpis_with_queries += 1
                    logger.info(f"✅ {kpi['id']} has query")
                elif kpi['id'] in TEST_KPIS:
                    logger.warning(f"⚠️ {kpi['id']} missing query")
        
        logger.info(f"\n📊 Summary: {kpis_with_queries}/{total_kpis} KPIs have queries")
        
    except Exception as e:
        logger.error(f"❌ Error reading JSON: {e}")


async def main():
    """Run all tests."""
    logger.info("🚀 Starting KPI System Tests\n")
    
    # Test 1: JSON structure
    test_json_structure()
    
    # Test 2: Query generation
    await test_query_generation()
    
    # Test 3: KPI calculation
    await test_kpi_calculation()
    
    # Test 4: Dashboard API (optional)
    await test_dashboard_endpoint()
    
    logger.info("\n✅ Tests completed!")


if __name__ == "__main__":
    asyncio.run(main())