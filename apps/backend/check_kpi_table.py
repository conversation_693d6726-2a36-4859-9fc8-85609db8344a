#\!/usr/bin/env python3
"""Check existing KPI table structure."""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from src.utils.learning_db_utils import get_db_manager
from sqlalchemy import text

try:
    db_manager = get_db_manager()
    
    with db_manager.get_session() as session:
        # Check if table exists and get columns
        result = session.execute(text("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'kpi_definitions'
            ORDER BY ordinal_position
        """))
        
        columns = result.fetchall()
        
        if columns:
            print("🔍 Existing kpi_definitions table structure:")
            for col_name, col_type in columns:
                print(f"  - {col_name}: {col_type}")
        else:
            print("❌ Table kpi_definitions not found")
            
        # Check for kpi_query_history
        result2 = session.execute(text("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_name = 'kpi_query_history'
        """))
        
        if result2.scalar() > 0:
            print("\n✅ Table kpi_query_history exists")
        else:
            print("\n❌ Table kpi_query_history not found")
            
except Exception as e:
    print(f"Error: {e}")
