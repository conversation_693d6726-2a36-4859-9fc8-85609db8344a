# 📊 Sistema de KPIs Dinâmicos com Geração Sob Demanda

## 🎯 Visão Geral

Este documento detalha a implementação de um sistema que gera queries KPI **automaticamente** na primeira vez que são solicitadas, usando o pipeline multi-agentes existente. Não há necessidade de scripts ou processos manuais - as queries são geradas sob demanda quando o usuário acessa o dashboard.

## 🏗️ Arquitetura da Solução

### 1. **Conceito Core: KPIs como Perguntas**

Em vez de criar um novo workflow complexo, vamos usar o sistema existente:

```python
# Transformar definições de KPI em perguntas naturais
kpi_to_question = {
    "total_volume": "Qual o volume total de operações dos últimos 12 meses para o cliente?",
    "average_spread": "Qual o spread médio das operações de câmbio dos últimos 6 meses?",
    # ... outros KPIs
}
```

### 2. **Fluxo de Geração Sob Demanda**

```mermaid
graph LR
    A[User Access Dashboard] --> B[KPI Service Request]
    B --> C{Query Exists?}
    C -->|No| D[Add to Generation Queue]
    C -->|Yes| E[Return Cached Value]
    D --> F[Background Worker]
    F --> G[Multi-Agent Pipeline]
    G --> H[Save Query to JSON]
    H --> I[Next Request Uses Query]
```

### 3. **Agentes Utilizados**

- **Enhanced Coordinator**: Orquestra o processo
- **Smart Query Generator**: Gera a SQL baseada no contexto
- **Query Validator**: Valida sintaxe e semântica
- **SQL Executor**: Testa a query com dados reais
- **Business Analyst**: Permite edição via linguagem natural

## 📋 Implementação Detalhada

### Como Funciona a Geração Sob Demanda

1. **Primeira Requisição ao Dashboard**
   - Usuário acessa o dashboard
   - KpiService tenta calcular KPIs
   - Para cada KPI sem query, é adicionado à fila de geração
   - Dashboard mostra valores hardcoded temporariamente

2. **Geração em Background**
   - Worker assíncrono processa a fila
   - Usa o pipeline multi-agentes para gerar cada query
   - Salva no JSON automaticamente
   - Próximas requisições já usam a query gerada

3. **Totalmente Transparente**
   - Usuário não precisa fazer nada
   - Queries são geradas conforme necessário
   - Sistema aprende e melhora com o uso

### Fase 2: Gerenciador de Queries KPI

```python
# apps/backend/src/services/kpi_query_manager.py

class KpiQueryManager:
    """
    Gerencia queries dinâmicas de KPIs com suporte a:
    - Carregamento de queries do JSON
    - Atualização via linguagem natural
    - Versionamento e rollback
    - Cache inteligente
    """
    
    def __init__(self):
        self.kpi_json_path = "src/config/setores/cambio/kpis-exchange-json.json"
        self._query_cache = {}
        
    def get_kpi_query(self, kpi_id: str) -> Optional[str]:
        """Retorna query do JSON ou None se não existir."""
        if kpi_id in self._query_cache:
            return self._query_cache[kpi_id]
            
        kpis_data = self._load_kpis_json()
        query = self._find_query_in_json(kpis_data, kpi_id)
        
        if query:
            self._query_cache[kpi_id] = query
            
        return query
    
    async def update_kpi_via_natural_language(self, kpi_id: str, instruction: str):
        """
        Permite editar KPI usando linguagem natural.
        Ex: "Mude o período do volume total para 6 meses"
        """
        current_query = self.get_kpi_query(kpi_id)
        
        # Usar BusinessAnalyst para processar a instrução
        updated_query = await self._process_with_business_analyst(
            current_query, 
            instruction,
            kpi_id
        )
        
        # Validar e salvar
        if await self._validate_query(updated_query):
            self._save_query_to_json(kpi_id, updated_query)
            return {"success": True, "new_query": updated_query}
        
        return {"success": False, "error": "Query inválida"}
```

### Fase 3: Integração com KpiService

```python
# Modificações em apps/backend/src/services/kpi_service.py

class KpiCalculationService:
    def __init__(self):
        # ... existing code ...
        self.query_manager = KpiQueryManager()  # Novo
    
    def _calculate_real_kpi_value(self, kpi_id: str, client_id: str):
        """Versão atualizada que usa queries dinâmicas."""
        
        # 1. Tentar obter query dinâmica
        dynamic_query = self.query_manager.get_kpi_query(kpi_id)
        
        if dynamic_query:
            logger.info(f"🎯 Usando query dinâmica para {kpi_id}")
            try:
                # Executar query dinâmica
                result = self._execute_dynamic_query(
                    dynamic_query,
                    {"client_id": 334}  # L2M mapping
                )
                
                # Cache o resultado
                self._set_cached_kpi_value(kpi_id, client_id, result)
                return result
                
            except Exception as e:
                logger.error(f"❌ Erro na query dinâmica: {e}")
                # Fallback para hardcoded
        
        # 2. Fallback para queries hardcoded (compatibilidade)
        logger.info(f"📌 Usando query hardcoded para {kpi_id}")
        return self._calculate_hardcoded_kpi(kpi_id, client_id)
```

## 🚀 Benefícios da Abordagem

### 1. **Simplicidade Máxima**
- Reutiliza 100% do sistema existente
- Não cria novos workflows ou agentes
- Mantém compatibilidade total

### 2. **Flexibilidade para Edição Natural**
```python
# Exemplo de uso futuro
await dashboard_api.edit_kpi(
    kpi_id="total_volume",
    instruction="Inclua apenas operações acima de R$ 10.000"
)
```

### 3. **Evolução Incremental**
- **MVP**: 6 KPIs com queries geradas
- **V2**: Interface para edição natural
- **V3**: Criação de novos KPIs via UI
- **V4**: KPIs compostos e agregações

## 📊 Estrutura do JSON Atualizado

```json
{
  "categories": [
    {
      "id": "operational",
      "kpis": [
        {
          "id": "total_volume",
          "name": "Volume Total Negociado",
          "description": "Volume total de operações",
          "sql_query": "SELECT COALESCE(SUM(valor_me), 0) FROM boleta WHERE data_operacao >= CURRENT_DATE - INTERVAL '12 months' AND id_cliente = :client_id",
          "query_metadata": {
            "generated_at": "2024-01-10T10:00:00Z",
            "generated_by": "multi_agent_system",
            "confidence": 0.95,
            "version": 1,
            "last_modified": null,
            "modification_history": []
          }
        }
      ]
    }
  ]
}
```

## 🔄 Processo de Geração Automática

### 1. **Detecção Automática**
```python
# Em KpiQueryManager.get_kpi_query()
if not query_exists:
    self._mark_for_generation(kpi_id)  # Adiciona à fila
    return None  # Usa hardcoded por enquanto
```

### 2. **Geração em Background**
```python
# Worker assíncrono processa a fila
async def _generation_worker(self):
    while True:
        kpi_id = await self._generation_queue.get()
        await self._generate_kpi_query(kpi_id)  # Usa pipeline
```

### 3. **Próximas Requisições**
- Query já está salva no JSON
- KpiService usa query dinâmica
- Performance otimizada com cache

## 🛡️ Garantias de Segurança

1. **Validação Dupla**
   - Query Validator valida sintaxe
   - Execution test valida resultado

2. **Fallback Automático**
   - Se query dinâmica falhar, usa hardcoded
   - Zero downtime durante transição

3. **Versionamento**
   - Histórico de modificações
   - Possibilidade de rollback

## 📈 Métricas de Sucesso

- **Performance**: Queries dinâmicas ≤ 100ms overhead
- **Confiabilidade**: 99.9% uptime com fallback
- **Flexibilidade**: 100% dos KPIs editáveis via NL
- **Adoção**: 6 KPIs migrados em 2 dias

## 🎯 Como Ativar o Sistema

### Deploy Imediato
1. **Nenhuma ação necessária** - Sistema já está integrado
2. **Primeiro acesso ao dashboard** - Inicia geração automática
3. **Aguardar alguns minutos** - Queries são geradas em background
4. **Pronto!** - Sistema usa queries dinâmicas automaticamente

### Semana 2: Edição Natural
1. Endpoint para edição via NL
2. UI para preview de queries
3. Sistema de aprovação

### Mês 1: Expansão
1. Novos KPIs via interface
2. KPIs compostos
3. Dashboards personalizados

## 💡 Exemplo Prático

```python
# Como o sistema funcionará na prática:

# 1. Usuário no dashboard clica em "Editar KPI"
# 2. Digite: "Mostre apenas operações dos últimos 3 meses"
# 3. Sistema processa:

instruction = "Mostre apenas operações dos últimos 3 meses"
result = await kpi_manager.update_kpi_via_natural_language(
    "total_volume", 
    instruction
)

# 4. Query é atualizada de:
# "... WHERE data_operacao >= CURRENT_DATE - INTERVAL '12 months'"
# Para:
# "... WHERE data_operacao >= CURRENT_DATE - INTERVAL '3 months'"

# 5. Dashboard atualiza automaticamente
```

## 🔚 Conclusão

Esta abordagem oferece o melhor equilíbrio entre:
- **Simplicidade**: Reutiliza tudo que existe
- **Poder**: Permite edição via linguagem natural
- **Segurança**: Mantém fallbacks e validações
- **Evolução**: Cresce conforme necessidade

O sistema está pronto para começar com 6 KPIs e evoluir para centenas, sempre mantendo a elegância e simplicidade que o DataHero4 já possui.